# Running Stage 
FROM linuxserver/ffmpeg:latest

WORKDIR /monibuca 

# Copy the pre-compiled binary from the build context
# The GitHub Actions workflow prepares 'monibuca_linux' in the context root

COPY monibuca_amd64 ./monibuca_amd64
COPY monibuca_arm64 ./monibuca_arm64

COPY admin.zip ./admin.zip

# Install tcpdump
RUN apt-get update && apt-get install -y tcpdump && rm -rf /var/lib/apt/lists/*

# Copy the configuration file from the build context
COPY example/default/config.yaml /etc/monibuca/config.yaml

# Export necessary ports 
EXPOSE 6000 8080 8443 1935 554 5060 9000-20000
EXPOSE 5060/udp 44944/udp

RUN if [ "$(uname -m)" = "aarch64" ]; then \
      mv ./monibuca_arm64 ./monibuca_linux; \
      rm ./monibuca_amd64; \
    else \
      mv ./monibuca_amd64 ./monibuca_linux; \
      rm ./monibuca_arm64; \
    fi


ENTRYPOINT [ "./monibuca_linux"]
CMD ["-c", "/etc/monibuca/config.yaml"]
