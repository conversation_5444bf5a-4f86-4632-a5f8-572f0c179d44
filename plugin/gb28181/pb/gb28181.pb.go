// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: gb28181.proto

//import "global.proto";

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 基础响应消息
type BaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseResponse) Reset() {
	*x = BaseResponse{}
	mi := &file_gb28181_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseResponse) ProtoMessage() {}

func (x *BaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseResponse.ProtoReflect.Descriptor instead.
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{0}
}

func (x *BaseResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BaseResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 请求和响应消息定义
type GetDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceRequest) Reset() {
	*x = GetDeviceRequest{}
	mi := &file_gb28181_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceRequest) ProtoMessage() {}

func (x *GetDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{1}
}

func (x *GetDeviceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetDevicesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Query         string                 `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`
	Status        bool                   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDevicesRequest) Reset() {
	*x = GetDevicesRequest{}
	mi := &file_gb28181_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDevicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDevicesRequest) ProtoMessage() {}

func (x *GetDevicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDevicesRequest.ProtoReflect.Descriptor instead.
func (*GetDevicesRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{2}
}

func (x *GetDevicesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetDevicesRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetDevicesRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *GetDevicesRequest) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

type DevicesPageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Data          []*Device              `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DevicesPageInfo) Reset() {
	*x = DevicesPageInfo{}
	mi := &file_gb28181_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DevicesPageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DevicesPageInfo) ProtoMessage() {}

func (x *DevicesPageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DevicesPageInfo.ProtoReflect.Descriptor instead.
func (*DevicesPageInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{3}
}

func (x *DevicesPageInfo) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DevicesPageInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DevicesPageInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *DevicesPageInfo) GetData() []*Device {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`
	Query         string                 `protobuf:"bytes,4,opt,name=query,proto3" json:"query,omitempty"`
	Online        bool                   `protobuf:"varint,5,opt,name=online,proto3" json:"online,omitempty"`
	ChannelType   bool                   `protobuf:"varint,6,opt,name=channelType,proto3" json:"channelType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetChannelsRequest) Reset() {
	*x = GetChannelsRequest{}
	mi := &file_gb28181_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetChannelsRequest) ProtoMessage() {}

func (x *GetChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetChannelsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{4}
}

func (x *GetChannelsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetChannelsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetChannelsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetChannelsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *GetChannelsRequest) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *GetChannelsRequest) GetChannelType() bool {
	if x != nil {
		return x.ChannelType
	}
	return false
}

type ChannelsPageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Channel             `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelsPageInfo) Reset() {
	*x = ChannelsPageInfo{}
	mi := &file_gb28181_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelsPageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelsPageInfo) ProtoMessage() {}

func (x *ChannelsPageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelsPageInfo.ProtoReflect.Descriptor instead.
func (*ChannelsPageInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{5}
}

func (x *ChannelsPageInfo) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChannelsPageInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChannelsPageInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ChannelsPageInfo) GetList() []*Channel {
	if x != nil {
		return x.List
	}
	return nil
}

type SyncDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncDeviceRequest) Reset() {
	*x = SyncDeviceRequest{}
	mi := &file_gb28181_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncDeviceRequest) ProtoMessage() {}

func (x *SyncDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncDeviceRequest.ProtoReflect.Descriptor instead.
func (*SyncDeviceRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{6}
}

func (x *SyncDeviceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type SyncStatus struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Current       int32                  `protobuf:"varint,4,opt,name=current,proto3" json:"current,omitempty"`
	ErrorMsg      string                 `protobuf:"bytes,5,opt,name=errorMsg,proto3" json:"errorMsg,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncStatus) Reset() {
	*x = SyncStatus{}
	mi := &file_gb28181_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncStatus) ProtoMessage() {}

func (x *SyncStatus) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncStatus.ProtoReflect.Descriptor instead.
func (*SyncStatus) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{7}
}

func (x *SyncStatus) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SyncStatus) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SyncStatus) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SyncStatus) GetCurrent() int32 {
	if x != nil {
		return x.Current
	}
	return 0
}

func (x *SyncStatus) GetErrorMsg() string {
	if x != nil {
		return x.ErrorMsg
	}
	return ""
}

type DeleteDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDeviceRequest) Reset() {
	*x = DeleteDeviceRequest{}
	mi := &file_gb28181_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeviceRequest) ProtoMessage() {}

func (x *DeleteDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeviceRequest.ProtoReflect.Descriptor instead.
func (*DeleteDeviceRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteDeviceRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type DeleteDeviceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	DeviceId      string                 `protobuf:"bytes,3,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDeviceResponse) Reset() {
	*x = DeleteDeviceResponse{}
	mi := &file_gb28181_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDeviceResponse) ProtoMessage() {}

func (x *DeleteDeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDeviceResponse.ProtoReflect.Descriptor instead.
func (*DeleteDeviceResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteDeviceResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeleteDeviceResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeleteDeviceResponse) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetSubChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Page          int32                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	Query         string                 `protobuf:"bytes,5,opt,name=query,proto3" json:"query,omitempty"`
	Online        bool                   `protobuf:"varint,6,opt,name=online,proto3" json:"online,omitempty"`
	ChannelType   bool                   `protobuf:"varint,7,opt,name=channelType,proto3" json:"channelType,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubChannelsRequest) Reset() {
	*x = GetSubChannelsRequest{}
	mi := &file_gb28181_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubChannelsRequest) ProtoMessage() {}

func (x *GetSubChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetSubChannelsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{10}
}

func (x *GetSubChannelsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetSubChannelsRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *GetSubChannelsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetSubChannelsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetSubChannelsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *GetSubChannelsRequest) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *GetSubChannelsRequest) GetChannelType() bool {
	if x != nil {
		return x.ChannelType
	}
	return false
}

type Channel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,3,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ParentId      string                 `protobuf:"bytes,4,opt,name=parentId,proto3" json:"parentId,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Manufacturer  string                 `protobuf:"bytes,6,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	Model         string                 `protobuf:"bytes,7,opt,name=model,proto3" json:"model,omitempty"`
	Owner         string                 `protobuf:"bytes,8,opt,name=owner,proto3" json:"owner,omitempty"`
	CivilCode     string                 `protobuf:"bytes,9,opt,name=civilCode,proto3" json:"civilCode,omitempty"`
	Address       string                 `protobuf:"bytes,10,opt,name=address,proto3" json:"address,omitempty"`
	Port          int32                  `protobuf:"varint,11,opt,name=port,proto3" json:"port,omitempty"`
	Parental      int32                  `protobuf:"varint,12,opt,name=parental,proto3" json:"parental,omitempty"`
	SafetyWay     int32                  `protobuf:"varint,13,opt,name=safetyWay,proto3" json:"safetyWay,omitempty"`
	RegisterWay   int32                  `protobuf:"varint,14,opt,name=registerWay,proto3" json:"registerWay,omitempty"`
	Secrecy       int32                  `protobuf:"varint,15,opt,name=secrecy,proto3" json:"secrecy,omitempty"`
	Status        string                 `protobuf:"bytes,16,opt,name=status,proto3" json:"status,omitempty"`
	GpsTime       *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=gpsTime,proto3" json:"gpsTime,omitempty"`
	Longitude     string                 `protobuf:"bytes,18,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude      string                 `protobuf:"bytes,19,opt,name=latitude,proto3" json:"latitude,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Channel) Reset() {
	*x = Channel{}
	mi := &file_gb28181_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Channel) ProtoMessage() {}

func (x *Channel) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Channel.ProtoReflect.Descriptor instead.
func (*Channel) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{11}
}

func (x *Channel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Channel) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Channel) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *Channel) GetParentId() string {
	if x != nil {
		return x.ParentId
	}
	return ""
}

func (x *Channel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Channel) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *Channel) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Channel) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *Channel) GetCivilCode() string {
	if x != nil {
		return x.CivilCode
	}
	return ""
}

func (x *Channel) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Channel) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Channel) GetParental() int32 {
	if x != nil {
		return x.Parental
	}
	return 0
}

func (x *Channel) GetSafetyWay() int32 {
	if x != nil {
		return x.SafetyWay
	}
	return 0
}

func (x *Channel) GetRegisterWay() int32 {
	if x != nil {
		return x.RegisterWay
	}
	return 0
}

func (x *Channel) GetSecrecy() int32 {
	if x != nil {
		return x.Secrecy
	}
	return 0
}

func (x *Channel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Channel) GetGpsTime() *timestamppb.Timestamp {
	if x != nil {
		return x.GpsTime
	}
	return nil
}

func (x *Channel) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *Channel) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

type Device struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	DeviceId          string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Name              string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Manufacturer      string                 `protobuf:"bytes,3,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`
	Model             string                 `protobuf:"bytes,4,opt,name=model,proto3" json:"model,omitempty"`
	Longitude         string                 `protobuf:"bytes,5,opt,name=longitude,proto3" json:"longitude,omitempty"`
	Latitude          string                 `protobuf:"bytes,6,opt,name=latitude,proto3" json:"latitude,omitempty"`
	Status            string                 `protobuf:"bytes,7,opt,name=status,proto3" json:"status,omitempty"`
	MediaIp           string                 `protobuf:"bytes,8,opt,name=mediaIp,proto3" json:"mediaIp,omitempty"`
	RegisterTime      *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=registerTime,proto3" json:"registerTime,omitempty"`
	UpdateTime        *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	KeepAliveTime     *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=keepAliveTime,proto3" json:"keepAliveTime,omitempty"`
	ChannelCount      int32                  `protobuf:"varint,12,opt,name=channelCount,proto3" json:"channelCount,omitempty"`
	Online            bool                   `protobuf:"varint,13,opt,name=online,proto3" json:"online,omitempty"`
	Channels          []*Channel             `protobuf:"bytes,14,rep,name=channels,proto3" json:"channels,omitempty"`
	SipIp             string                 `protobuf:"bytes,15,opt,name=sipIp,proto3" json:"sipIp,omitempty"`
	StreamMode        string                 `protobuf:"bytes,16,opt,name=streamMode,proto3" json:"streamMode,omitempty"`
	Password          string                 `protobuf:"bytes,17,opt,name=password,proto3" json:"password,omitempty"`
	SubscribeCatalog  bool                   `protobuf:"varint,18,opt,name=subscribeCatalog,proto3" json:"subscribeCatalog,omitempty"`
	SubscribePosition bool                   `protobuf:"varint,19,opt,name=subscribePosition,proto3" json:"subscribePosition,omitempty"`
	SubscribeAlarm    bool                   `protobuf:"varint,20,opt,name=subscribeAlarm,proto3" json:"subscribeAlarm,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Device) Reset() {
	*x = Device{}
	mi := &file_gb28181_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Device) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Device) ProtoMessage() {}

func (x *Device) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Device.ProtoReflect.Descriptor instead.
func (*Device) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{12}
}

func (x *Device) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *Device) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Device) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *Device) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Device) GetLongitude() string {
	if x != nil {
		return x.Longitude
	}
	return ""
}

func (x *Device) GetLatitude() string {
	if x != nil {
		return x.Latitude
	}
	return ""
}

func (x *Device) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Device) GetMediaIp() string {
	if x != nil {
		return x.MediaIp
	}
	return ""
}

func (x *Device) GetRegisterTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RegisterTime
	}
	return nil
}

func (x *Device) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Device) GetKeepAliveTime() *timestamppb.Timestamp {
	if x != nil {
		return x.KeepAliveTime
	}
	return nil
}

func (x *Device) GetChannelCount() int32 {
	if x != nil {
		return x.ChannelCount
	}
	return 0
}

func (x *Device) GetOnline() bool {
	if x != nil {
		return x.Online
	}
	return false
}

func (x *Device) GetChannels() []*Channel {
	if x != nil {
		return x.Channels
	}
	return nil
}

func (x *Device) GetSipIp() string {
	if x != nil {
		return x.SipIp
	}
	return ""
}

func (x *Device) GetStreamMode() string {
	if x != nil {
		return x.StreamMode
	}
	return ""
}

func (x *Device) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Device) GetSubscribeCatalog() bool {
	if x != nil {
		return x.SubscribeCatalog
	}
	return false
}

func (x *Device) GetSubscribePosition() bool {
	if x != nil {
		return x.SubscribePosition
	}
	return false
}

func (x *Device) GetSubscribeAlarm() bool {
	if x != nil {
		return x.SubscribeAlarm
	}
	return false
}

type ResponseList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Device              `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseList) Reset() {
	*x = ResponseList{}
	mi := &file_gb28181_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseList) ProtoMessage() {}

func (x *ResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseList.ProtoReflect.Descriptor instead.
func (*ResponseList) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{13}
}

func (x *ResponseList) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseList) GetData() []*Device {
	if x != nil {
		return x.Data
	}
	return nil
}

// 新增的请求和响应消息定义
type ChangeAudioRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChannelId     int32                  `protobuf:"varint,1,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Audio         bool                   `protobuf:"varint,2,opt,name=audio,proto3" json:"audio,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangeAudioRequest) Reset() {
	*x = ChangeAudioRequest{}
	mi := &file_gb28181_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangeAudioRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeAudioRequest) ProtoMessage() {}

func (x *ChangeAudioRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeAudioRequest.ProtoReflect.Descriptor instead.
func (*ChangeAudioRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{14}
}

func (x *ChangeAudioRequest) GetChannelId() int32 {
	if x != nil {
		return x.ChannelId
	}
	return 0
}

func (x *ChangeAudioRequest) GetAudio() bool {
	if x != nil {
		return x.Audio
	}
	return false
}

type UpdateTransportRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	StreamMode    string                 `protobuf:"bytes,2,opt,name=streamMode,proto3" json:"streamMode,omitempty"` // UDP, TCP-ACTIVE, TCP-PASSIVE
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTransportRequest) Reset() {
	*x = UpdateTransportRequest{}
	mi := &file_gb28181_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTransportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTransportRequest) ProtoMessage() {}

func (x *UpdateTransportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTransportRequest.ProtoReflect.Descriptor instead.
func (*UpdateTransportRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{15}
}

func (x *UpdateTransportRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *UpdateTransportRequest) GetStreamMode() string {
	if x != nil {
		return x.StreamMode
	}
	return ""
}

type GetDeviceStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceStatusRequest) Reset() {
	*x = GetDeviceStatusRequest{}
	mi := &file_gb28181_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceStatusRequest) ProtoMessage() {}

func (x *GetDeviceStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceStatusRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{16}
}

func (x *GetDeviceStatusRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type DeviceStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Status        string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceStatusResponse) Reset() {
	*x = DeviceStatusResponse{}
	mi := &file_gb28181_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceStatusResponse) ProtoMessage() {}

func (x *DeviceStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceStatusResponse.ProtoReflect.Descriptor instead.
func (*DeviceStatusResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{17}
}

func (x *DeviceStatusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeviceStatusResponse) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

type GetDeviceAlarmRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	StartPriority string                 `protobuf:"bytes,2,opt,name=startPriority,proto3" json:"startPriority,omitempty"`
	EndPriority   string                 `protobuf:"bytes,3,opt,name=endPriority,proto3" json:"endPriority,omitempty"`
	AlarmMethod   string                 `protobuf:"bytes,4,opt,name=alarmMethod,proto3" json:"alarmMethod,omitempty"`
	AlarmType     string                 `protobuf:"bytes,5,opt,name=alarmType,proto3" json:"alarmType,omitempty"`
	StartTime     string                 `protobuf:"bytes,6,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       string                 `protobuf:"bytes,7,opt,name=endTime,proto3" json:"endTime,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeviceAlarmRequest) Reset() {
	*x = GetDeviceAlarmRequest{}
	mi := &file_gb28181_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeviceAlarmRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeviceAlarmRequest) ProtoMessage() {}

func (x *GetDeviceAlarmRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeviceAlarmRequest.ProtoReflect.Descriptor instead.
func (*GetDeviceAlarmRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{18}
}

func (x *GetDeviceAlarmRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetStartPriority() string {
	if x != nil {
		return x.StartPriority
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetEndPriority() string {
	if x != nil {
		return x.EndPriority
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetAlarmMethod() string {
	if x != nil {
		return x.AlarmMethod
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetAlarmType() string {
	if x != nil {
		return x.AlarmType
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *GetDeviceAlarmRequest) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

type DeviceAlarmResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*AlarmInfo           `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceAlarmResponse) Reset() {
	*x = DeviceAlarmResponse{}
	mi := &file_gb28181_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceAlarmResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceAlarmResponse) ProtoMessage() {}

func (x *DeviceAlarmResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceAlarmResponse.ProtoReflect.Descriptor instead.
func (*DeviceAlarmResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{19}
}

func (x *DeviceAlarmResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceAlarmResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeviceAlarmResponse) GetData() []*AlarmInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type AlarmInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	DeviceId         string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	AlarmPriority    string                 `protobuf:"bytes,2,opt,name=alarmPriority,proto3" json:"alarmPriority,omitempty"`
	AlarmMethod      string                 `protobuf:"bytes,3,opt,name=alarmMethod,proto3" json:"alarmMethod,omitempty"`
	AlarmTime        string                 `protobuf:"bytes,4,opt,name=alarmTime,proto3" json:"alarmTime,omitempty"`
	AlarmDescription string                 `protobuf:"bytes,5,opt,name=alarmDescription,proto3" json:"alarmDescription,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AlarmInfo) Reset() {
	*x = AlarmInfo{}
	mi := &file_gb28181_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmInfo) ProtoMessage() {}

func (x *AlarmInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmInfo.ProtoReflect.Descriptor instead.
func (*AlarmInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{20}
}

func (x *AlarmInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AlarmInfo) GetAlarmPriority() string {
	if x != nil {
		return x.AlarmPriority
	}
	return ""
}

func (x *AlarmInfo) GetAlarmMethod() string {
	if x != nil {
		return x.AlarmMethod
	}
	return ""
}

func (x *AlarmInfo) GetAlarmTime() string {
	if x != nil {
		return x.AlarmTime
	}
	return ""
}

func (x *AlarmInfo) GetAlarmDescription() string {
	if x != nil {
		return x.AlarmDescription
	}
	return ""
}

type GetSyncStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSyncStatusRequest) Reset() {
	*x = GetSyncStatusRequest{}
	mi := &file_gb28181_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSyncStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSyncStatusRequest) ProtoMessage() {}

func (x *GetSyncStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSyncStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSyncStatusRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{21}
}

func (x *GetSyncStatusRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type GetSubscribeInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSubscribeInfoRequest) Reset() {
	*x = GetSubscribeInfoRequest{}
	mi := &file_gb28181_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSubscribeInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubscribeInfoRequest) ProtoMessage() {}

func (x *GetSubscribeInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubscribeInfoRequest.ProtoReflect.Descriptor instead.
func (*GetSubscribeInfoRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{22}
}

func (x *GetSubscribeInfoRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

type SubscribeInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	DialogState   map[string]int32       `protobuf:"bytes,3,rep,name=dialogState,proto3" json:"dialogState,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SubscribeInfoResponse) Reset() {
	*x = SubscribeInfoResponse{}
	mi := &file_gb28181_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SubscribeInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubscribeInfoResponse) ProtoMessage() {}

func (x *SubscribeInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubscribeInfoResponse.ProtoReflect.Descriptor instead.
func (*SubscribeInfoResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{23}
}

func (x *SubscribeInfoResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SubscribeInfoResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SubscribeInfoResponse) GetDialogState() map[string]int32 {
	if x != nil {
		return x.DialogState
	}
	return nil
}

type GetSnapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Mark          string                 `protobuf:"bytes,3,opt,name=mark,proto3" json:"mark,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSnapRequest) Reset() {
	*x = GetSnapRequest{}
	mi := &file_gb28181_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSnapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSnapRequest) ProtoMessage() {}

func (x *GetSnapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSnapRequest.ProtoReflect.Descriptor instead.
func (*GetSnapRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{24}
}

func (x *GetSnapRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GetSnapRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *GetSnapRequest) GetMark() string {
	if x != nil {
		return x.Mark
	}
	return ""
}

type SnapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	ImageData     []byte                 `protobuf:"bytes,3,opt,name=imageData,proto3" json:"imageData,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SnapResponse) Reset() {
	*x = SnapResponse{}
	mi := &file_gb28181_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SnapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnapResponse) ProtoMessage() {}

func (x *SnapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnapResponse.ProtoReflect.Descriptor instead.
func (*SnapResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{25}
}

func (x *SnapResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SnapResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SnapResponse) GetImageData() []byte {
	if x != nil {
		return x.ImageData
	}
	return nil
}

type GetRawChannelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRawChannelRequest) Reset() {
	*x = GetRawChannelRequest{}
	mi := &file_gb28181_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRawChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRawChannelRequest) ProtoMessage() {}

func (x *GetRawChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRawChannelRequest.ProtoReflect.Descriptor instead.
func (*GetRawChannelRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{26}
}

func (x *GetRawChannelRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeviceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *Device                `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeviceResponse) Reset() {
	*x = DeviceResponse{}
	mi := &file_gb28181_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeviceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeviceResponse) ProtoMessage() {}

func (x *DeviceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeviceResponse.ProtoReflect.Descriptor instead.
func (*DeviceResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{27}
}

func (x *DeviceResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *DeviceResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DeviceResponse) GetData() *Device {
	if x != nil {
		return x.Data
	}
	return nil
}

type ChannelResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *Channel               `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelResponse) Reset() {
	*x = ChannelResponse{}
	mi := &file_gb28181_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelResponse) ProtoMessage() {}

func (x *ChannelResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelResponse.ProtoReflect.Descriptor instead.
func (*ChannelResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{28}
}

func (x *ChannelResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChannelResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ChannelResponse) GetData() *Channel {
	if x != nil {
		return x.Data
	}
	return nil
}

type PlayRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayRequest) Reset() {
	*x = PlayRequest{}
	mi := &file_gb28181_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayRequest) ProtoMessage() {}

func (x *PlayRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayRequest.ProtoReflect.Descriptor instead.
func (*PlayRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{29}
}

func (x *PlayRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PlayRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

type PlaybackRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"` // 开始时间
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`     // 结束时间
	Range         string                 `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"` // 时间范围，可选
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaybackRequest) Reset() {
	*x = PlaybackRequest{}
	mi := &file_gb28181_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaybackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackRequest) ProtoMessage() {}

func (x *PlaybackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackRequest.ProtoReflect.Descriptor instead.
func (*PlaybackRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{30}
}

func (x *PlaybackRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PlaybackRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *PlaybackRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *PlaybackRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *PlaybackRequest) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

type PlayResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	StreamInfo    *StreamInfo            `protobuf:"bytes,3,opt,name=stream_info,json=streamInfo,proto3" json:"stream_info,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlayResponse) Reset() {
	*x = PlayResponse{}
	mi := &file_gb28181_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlayResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlayResponse) ProtoMessage() {}

func (x *PlayResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlayResponse.ProtoReflect.Descriptor instead.
func (*PlayResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{31}
}

func (x *PlayResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlayResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlayResponse) GetStreamInfo() *StreamInfo {
	if x != nil {
		return x.StreamInfo
	}
	return nil
}

type StreamInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Stream        string                 `protobuf:"bytes,1,opt,name=stream,proto3" json:"stream,omitempty"`
	App           string                 `protobuf:"bytes,2,opt,name=app,proto3" json:"app,omitempty"`
	Ip            string                 `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	Port          int32                  `protobuf:"varint,4,opt,name=port,proto3" json:"port,omitempty"`
	Ssrc          string                 `protobuf:"bytes,5,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StreamInfo) Reset() {
	*x = StreamInfo{}
	mi := &file_gb28181_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StreamInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StreamInfo) ProtoMessage() {}

func (x *StreamInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StreamInfo.ProtoReflect.Descriptor instead.
func (*StreamInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{32}
}

func (x *StreamInfo) GetStream() string {
	if x != nil {
		return x.Stream
	}
	return ""
}

func (x *StreamInfo) GetApp() string {
	if x != nil {
		return x.App
	}
	return ""
}

func (x *StreamInfo) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *StreamInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *StreamInfo) GetSsrc() string {
	if x != nil {
		return x.Ssrc
	}
	return ""
}

type ConvertStopRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	MediaServerId string                 `protobuf:"bytes,2,opt,name=mediaServerId,proto3" json:"mediaServerId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertStopRequest) Reset() {
	*x = ConvertStopRequest{}
	mi := &file_gb28181_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertStopRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertStopRequest) ProtoMessage() {}

func (x *ConvertStopRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertStopRequest.ProtoReflect.Descriptor instead.
func (*ConvertStopRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{33}
}

func (x *ConvertStopRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *ConvertStopRequest) GetMediaServerId() string {
	if x != nil {
		return x.MediaServerId
	}
	return ""
}

type BroadcastRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Timeout       int32                  `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	BroadcastMode bool                   `protobuf:"varint,4,opt,name=broadcastMode,proto3" json:"broadcastMode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastRequest) Reset() {
	*x = BroadcastRequest{}
	mi := &file_gb28181_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastRequest) ProtoMessage() {}

func (x *BroadcastRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastRequest.ProtoReflect.Descriptor instead.
func (*BroadcastRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{34}
}

func (x *BroadcastRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *BroadcastRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *BroadcastRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *BroadcastRequest) GetBroadcastMode() bool {
	if x != nil {
		return x.BroadcastMode
	}
	return false
}

type BroadcastResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	StreamId      string                 `protobuf:"bytes,3,opt,name=streamId,proto3" json:"streamId,omitempty"`
	Url           string                 `protobuf:"bytes,4,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BroadcastResponse) Reset() {
	*x = BroadcastResponse{}
	mi := &file_gb28181_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BroadcastResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BroadcastResponse) ProtoMessage() {}

func (x *BroadcastResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BroadcastResponse.ProtoReflect.Descriptor instead.
func (*BroadcastResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{35}
}

func (x *BroadcastResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *BroadcastResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *BroadcastResponse) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

func (x *BroadcastResponse) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type SSRCInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Ssrc          string                 `protobuf:"bytes,3,opt,name=ssrc,proto3" json:"ssrc,omitempty"`
	StreamId      string                 `protobuf:"bytes,4,opt,name=streamId,proto3" json:"streamId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SSRCInfo) Reset() {
	*x = SSRCInfo{}
	mi := &file_gb28181_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SSRCInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSRCInfo) ProtoMessage() {}

func (x *SSRCInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSRCInfo.ProtoReflect.Descriptor instead.
func (*SSRCInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{36}
}

func (x *SSRCInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SSRCInfo) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *SSRCInfo) GetSsrc() string {
	if x != nil {
		return x.Ssrc
	}
	return ""
}

func (x *SSRCInfo) GetStreamId() string {
	if x != nil {
		return x.StreamId
	}
	return ""
}

type SSRCListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*SSRCInfo            `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	Count         int32                  `protobuf:"varint,4,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SSRCListResponse) Reset() {
	*x = SSRCListResponse{}
	mi := &file_gb28181_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SSRCListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SSRCListResponse) ProtoMessage() {}

func (x *SSRCListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SSRCListResponse.ProtoReflect.Descriptor instead.
func (*SSRCListResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{37}
}

func (x *SSRCListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SSRCListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SSRCListResponse) GetData() []*SSRCInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *SSRCListResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// Platform消息定义
type Platform struct {
	state                   protoimpl.MessageState `protogen:"open.v1"`
	Id                      uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                            // id(数据库中)
	Enable                  bool                   `protobuf:"varint,2,opt,name=enable,proto3" json:"enable,omitempty"`                                    // 是否启用
	Name                    string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`                                         // 名称
	ServerGBId              string                 `protobuf:"bytes,4,opt,name=serverGBId,proto3" json:"serverGBId,omitempty"`                             // SIP服务国标编码
	ServerGBDomain          string                 `protobuf:"bytes,5,opt,name=serverGBDomain,proto3" json:"serverGBDomain,omitempty"`                     // SIP服务国标域
	ServerIp                string                 `protobuf:"bytes,6,opt,name=serverIp,proto3" json:"serverIp,omitempty"`                                 // SIP服务IP
	ServerPort              int32                  `protobuf:"varint,7,opt,name=serverPort,proto3" json:"serverPort,omitempty"`                            // SIP服务端口
	DeviceGBId              string                 `protobuf:"bytes,8,opt,name=deviceGBId,proto3" json:"deviceGBId,omitempty"`                             // 设备国标编号
	DeviceIp                string                 `protobuf:"bytes,9,opt,name=deviceIp,proto3" json:"deviceIp,omitempty"`                                 // 设备ip
	DevicePort              int32                  `protobuf:"varint,10,opt,name=devicePort,proto3" json:"devicePort,omitempty"`                           // 设备端口
	Username                string                 `protobuf:"bytes,11,opt,name=username,proto3" json:"username,omitempty"`                                // SIP认证用户名
	Password                string                 `protobuf:"bytes,12,opt,name=password,proto3" json:"password,omitempty"`                                // SIP认证密码
	Expires                 int32                  `protobuf:"varint,13,opt,name=expires,proto3" json:"expires,omitempty"`                                 // 注册周期(秒)
	KeepTimeout             int32                  `protobuf:"varint,14,opt,name=keepTimeout,proto3" json:"keepTimeout,omitempty"`                         // 心跳周期(秒)
	Transport               string                 `protobuf:"bytes,15,opt,name=transport,proto3" json:"transport,omitempty"`                              // 传输协议
	CharacterSet            string                 `protobuf:"bytes,16,opt,name=characterSet,proto3" json:"characterSet,omitempty"`                        // 字符集
	Ptz                     bool                   `protobuf:"varint,17,opt,name=ptz,proto3" json:"ptz,omitempty"`                                         // 允许云台控制
	Rtcp                    bool                   `protobuf:"varint,18,opt,name=rtcp,proto3" json:"rtcp,omitempty"`                                       // RTCP流保活
	Status                  bool                   `protobuf:"varint,19,opt,name=status,proto3" json:"status,omitempty"`                                   // 在线状态
	ChannelCount            int32                  `protobuf:"varint,20,opt,name=channelCount,proto3" json:"channelCount,omitempty"`                       // 通道数量
	CatalogSubscribe        bool                   `protobuf:"varint,21,opt,name=catalogSubscribe,proto3" json:"catalogSubscribe,omitempty"`               // 已被订阅目录信息
	AlarmSubscribe          bool                   `protobuf:"varint,22,opt,name=alarmSubscribe,proto3" json:"alarmSubscribe,omitempty"`                   // 已被订阅报警信息
	MobilePositionSubscribe bool                   `protobuf:"varint,23,opt,name=mobilePositionSubscribe,proto3" json:"mobilePositionSubscribe,omitempty"` // 已被订阅移动位置信息
	CatalogGroup            int32                  `protobuf:"varint,24,opt,name=catalogGroup,proto3" json:"catalogGroup,omitempty"`                       // 目录分组大小
	UpdateTime              string                 `protobuf:"bytes,25,opt,name=updateTime,proto3" json:"updateTime,omitempty"`                            // 最后更新时间
	CreateTime              string                 `protobuf:"bytes,26,opt,name=createTime,proto3" json:"createTime,omitempty"`                            // 创建时间
	AsMessageChannel        bool                   `protobuf:"varint,27,opt,name=asMessageChannel,proto3" json:"asMessageChannel,omitempty"`               // 是否作为消息通道
	SendStreamIp            string                 `protobuf:"bytes,28,opt,name=sendStreamIp,proto3" json:"sendStreamIp,omitempty"`                        // 点播回复200OK使用的IP
	AutoPushChannel         bool                   `protobuf:"varint,29,opt,name=autoPushChannel,proto3" json:"autoPushChannel,omitempty"`                 // 是否自动推送通道变化
	CatalogWithPlatform     int32                  `protobuf:"varint,30,opt,name=catalogWithPlatform,proto3" json:"catalogWithPlatform,omitempty"`         // 目录信息包含平台信息
	CatalogWithGroup        int32                  `protobuf:"varint,31,opt,name=catalogWithGroup,proto3" json:"catalogWithGroup,omitempty"`               // 目录信息包含分组信息
	CatalogWithRegion       int32                  `protobuf:"varint,32,opt,name=catalogWithRegion,proto3" json:"catalogWithRegion,omitempty"`             // 目录信息包含行政区划
	CivilCode               string                 `protobuf:"bytes,33,opt,name=civilCode,proto3" json:"civilCode,omitempty"`                              // 行政区划代码
	Manufacturer            string                 `protobuf:"bytes,34,opt,name=manufacturer,proto3" json:"manufacturer,omitempty"`                        // 平台厂商
	Model                   string                 `protobuf:"bytes,35,opt,name=model,proto3" json:"model,omitempty"`                                      // 平台型号
	Address                 string                 `protobuf:"bytes,36,opt,name=address,proto3" json:"address,omitempty"`                                  // 平台安装地址
	RegisterWay             int32                  `protobuf:"varint,37,opt,name=registerWay,proto3" json:"registerWay,omitempty"`                         // 注册方式
	Secrecy                 int32                  `protobuf:"varint,38,opt,name=secrecy,proto3" json:"secrecy,omitempty"`                                 // 保密属性
	unknownFields           protoimpl.UnknownFields
	sizeCache               protoimpl.SizeCache
}

func (x *Platform) Reset() {
	*x = Platform{}
	mi := &file_gb28181_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Platform) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Platform) ProtoMessage() {}

func (x *Platform) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Platform.ProtoReflect.Descriptor instead.
func (*Platform) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{38}
}

func (x *Platform) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Platform) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Platform) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Platform) GetServerGBId() string {
	if x != nil {
		return x.ServerGBId
	}
	return ""
}

func (x *Platform) GetServerGBDomain() string {
	if x != nil {
		return x.ServerGBDomain
	}
	return ""
}

func (x *Platform) GetServerIp() string {
	if x != nil {
		return x.ServerIp
	}
	return ""
}

func (x *Platform) GetServerPort() int32 {
	if x != nil {
		return x.ServerPort
	}
	return 0
}

func (x *Platform) GetDeviceGBId() string {
	if x != nil {
		return x.DeviceGBId
	}
	return ""
}

func (x *Platform) GetDeviceIp() string {
	if x != nil {
		return x.DeviceIp
	}
	return ""
}

func (x *Platform) GetDevicePort() int32 {
	if x != nil {
		return x.DevicePort
	}
	return 0
}

func (x *Platform) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Platform) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *Platform) GetExpires() int32 {
	if x != nil {
		return x.Expires
	}
	return 0
}

func (x *Platform) GetKeepTimeout() int32 {
	if x != nil {
		return x.KeepTimeout
	}
	return 0
}

func (x *Platform) GetTransport() string {
	if x != nil {
		return x.Transport
	}
	return ""
}

func (x *Platform) GetCharacterSet() string {
	if x != nil {
		return x.CharacterSet
	}
	return ""
}

func (x *Platform) GetPtz() bool {
	if x != nil {
		return x.Ptz
	}
	return false
}

func (x *Platform) GetRtcp() bool {
	if x != nil {
		return x.Rtcp
	}
	return false
}

func (x *Platform) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *Platform) GetChannelCount() int32 {
	if x != nil {
		return x.ChannelCount
	}
	return 0
}

func (x *Platform) GetCatalogSubscribe() bool {
	if x != nil {
		return x.CatalogSubscribe
	}
	return false
}

func (x *Platform) GetAlarmSubscribe() bool {
	if x != nil {
		return x.AlarmSubscribe
	}
	return false
}

func (x *Platform) GetMobilePositionSubscribe() bool {
	if x != nil {
		return x.MobilePositionSubscribe
	}
	return false
}

func (x *Platform) GetCatalogGroup() int32 {
	if x != nil {
		return x.CatalogGroup
	}
	return 0
}

func (x *Platform) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *Platform) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Platform) GetAsMessageChannel() bool {
	if x != nil {
		return x.AsMessageChannel
	}
	return false
}

func (x *Platform) GetSendStreamIp() string {
	if x != nil {
		return x.SendStreamIp
	}
	return ""
}

func (x *Platform) GetAutoPushChannel() bool {
	if x != nil {
		return x.AutoPushChannel
	}
	return false
}

func (x *Platform) GetCatalogWithPlatform() int32 {
	if x != nil {
		return x.CatalogWithPlatform
	}
	return 0
}

func (x *Platform) GetCatalogWithGroup() int32 {
	if x != nil {
		return x.CatalogWithGroup
	}
	return 0
}

func (x *Platform) GetCatalogWithRegion() int32 {
	if x != nil {
		return x.CatalogWithRegion
	}
	return 0
}

func (x *Platform) GetCivilCode() string {
	if x != nil {
		return x.CivilCode
	}
	return ""
}

func (x *Platform) GetManufacturer() string {
	if x != nil {
		return x.Manufacturer
	}
	return ""
}

func (x *Platform) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *Platform) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Platform) GetRegisterWay() int32 {
	if x != nil {
		return x.RegisterWay
	}
	return 0
}

func (x *Platform) GetSecrecy() int32 {
	if x != nil {
		return x.Secrecy
	}
	return 0
}

// 获取平台请求
type GetPlatformRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPlatformRequest) Reset() {
	*x = GetPlatformRequest{}
	mi := &file_gb28181_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPlatformRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPlatformRequest) ProtoMessage() {}

func (x *GetPlatformRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPlatformRequest.ProtoReflect.Descriptor instead.
func (*GetPlatformRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{39}
}

func (x *GetPlatformRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除平台请求
type DeletePlatformRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlatformRequest) Reset() {
	*x = DeletePlatformRequest{}
	mi := &file_gb28181_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlatformRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlatformRequest) ProtoMessage() {}

func (x *DeletePlatformRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlatformRequest.ProtoReflect.Descriptor instead.
func (*DeletePlatformRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{40}
}

func (x *DeletePlatformRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 平台列表请求
type ListPlatformsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Count         int32                  `protobuf:"varint,2,opt,name=count,proto3" json:"count,omitempty"`
	Query         string                 `protobuf:"bytes,3,opt,name=query,proto3" json:"query,omitempty"`
	Status        bool                   `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPlatformsRequest) Reset() {
	*x = ListPlatformsRequest{}
	mi := &file_gb28181_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPlatformsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPlatformsRequest) ProtoMessage() {}

func (x *ListPlatformsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPlatformsRequest.ProtoReflect.Descriptor instead.
func (*ListPlatformsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{41}
}

func (x *ListPlatformsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListPlatformsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *ListPlatformsRequest) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *ListPlatformsRequest) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

// 平台响应
type PlatformResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *Platform              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformResponse) Reset() {
	*x = PlatformResponse{}
	mi := &file_gb28181_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformResponse) ProtoMessage() {}

func (x *PlatformResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformResponse.ProtoReflect.Descriptor instead.
func (*PlatformResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{42}
}

func (x *PlatformResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlatformResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlatformResponse) GetData() *Platform {
	if x != nil {
		return x.Data
	}
	return nil
}

// 平台分页信息
type PlatformsPageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Platform            `protobuf:"bytes,4,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformsPageInfo) Reset() {
	*x = PlatformsPageInfo{}
	mi := &file_gb28181_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformsPageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformsPageInfo) ProtoMessage() {}

func (x *PlatformsPageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformsPageInfo.ProtoReflect.Descriptor instead.
func (*PlatformsPageInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{43}
}

func (x *PlatformsPageInfo) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlatformsPageInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlatformsPageInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *PlatformsPageInfo) GetList() []*Platform {
	if x != nil {
		return x.List
	}
	return nil
}

type QueryRecordRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	Range         string                 `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryRecordRequest) Reset() {
	*x = QueryRecordRequest{}
	mi := &file_gb28181_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRecordRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRecordRequest) ProtoMessage() {}

func (x *QueryRecordRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRecordRequest.ProtoReflect.Descriptor instead.
func (*QueryRecordRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{44}
}

func (x *QueryRecordRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *QueryRecordRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *QueryRecordRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *QueryRecordRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *QueryRecordRequest) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

type QueryRecordResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*RecordItem          `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	DeviceId      string                 `protobuf:"bytes,4,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	ChannelId     string                 `protobuf:"bytes,5,opt,name=channel_id,json=channelId,proto3" json:"channel_id,omitempty"`
	Sn            string                 `protobuf:"bytes,6,opt,name=sn,proto3" json:"sn,omitempty"`
	Name          string                 `protobuf:"bytes,7,opt,name=name,proto3" json:"name,omitempty"`
	SumNum        int32                  `protobuf:"varint,8,opt,name=sum_num,json=sumNum,proto3" json:"sum_num,omitempty"`
	Count         int32                  `protobuf:"varint,9,opt,name=count,proto3" json:"count,omitempty"`
	LastTime      *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=last_time,json=lastTime,proto3" json:"last_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryRecordResponse) Reset() {
	*x = QueryRecordResponse{}
	mi := &file_gb28181_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryRecordResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryRecordResponse) ProtoMessage() {}

func (x *QueryRecordResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryRecordResponse.ProtoReflect.Descriptor instead.
func (*QueryRecordResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{45}
}

func (x *QueryRecordResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *QueryRecordResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *QueryRecordResponse) GetData() []*RecordItem {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *QueryRecordResponse) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *QueryRecordResponse) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *QueryRecordResponse) GetSn() string {
	if x != nil {
		return x.Sn
	}
	return ""
}

func (x *QueryRecordResponse) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QueryRecordResponse) GetSumNum() int32 {
	if x != nil {
		return x.SumNum
	}
	return 0
}

func (x *QueryRecordResponse) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *QueryRecordResponse) GetLastTime() *timestamppb.Timestamp {
	if x != nil {
		return x.LastTime
	}
	return nil
}

type RecordItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	FilePath      string                 `protobuf:"bytes,3,opt,name=filePath,proto3" json:"filePath,omitempty"`
	Address       string                 `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	StartTime     string                 `protobuf:"bytes,5,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       string                 `protobuf:"bytes,6,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Secrecy       int32                  `protobuf:"varint,7,opt,name=secrecy,proto3" json:"secrecy,omitempty"`
	Type          string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`
	RecorderId    string                 `protobuf:"bytes,9,opt,name=recorderId,proto3" json:"recorderId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordItem) Reset() {
	*x = RecordItem{}
	mi := &file_gb28181_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordItem) ProtoMessage() {}

func (x *RecordItem) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordItem.ProtoReflect.Descriptor instead.
func (*RecordItem) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{46}
}

func (x *RecordItem) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RecordItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RecordItem) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *RecordItem) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *RecordItem) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *RecordItem) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *RecordItem) GetSecrecy() int32 {
	if x != nil {
		return x.Secrecy
	}
	return 0
}

func (x *RecordItem) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *RecordItem) GetRecorderId() string {
	if x != nil {
		return x.RecorderId
	}
	return ""
}

type PtzControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Ptzcmd        string                 `protobuf:"bytes,3,opt,name=ptzcmd,proto3" json:"ptzcmd,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PtzControlRequest) Reset() {
	*x = PtzControlRequest{}
	mi := &file_gb28181_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PtzControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PtzControlRequest) ProtoMessage() {}

func (x *PtzControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PtzControlRequest.ProtoReflect.Descriptor instead.
func (*PtzControlRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{47}
}

func (x *PtzControlRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PtzControlRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *PtzControlRequest) GetPtzcmd() string {
	if x != nil {
		return x.Ptzcmd
	}
	return ""
}

type IrisControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"` // in, out, stop
	Speed         int32                  `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`    // 0-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IrisControlRequest) Reset() {
	*x = IrisControlRequest{}
	mi := &file_gb28181_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IrisControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IrisControlRequest) ProtoMessage() {}

func (x *IrisControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IrisControlRequest.ProtoReflect.Descriptor instead.
func (*IrisControlRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{48}
}

func (x *IrisControlRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *IrisControlRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *IrisControlRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *IrisControlRequest) GetSpeed() int32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type FocusControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"` // near, far, stop
	Speed         int32                  `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`    // 0-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FocusControlRequest) Reset() {
	*x = FocusControlRequest{}
	mi := &file_gb28181_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FocusControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FocusControlRequest) ProtoMessage() {}

func (x *FocusControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FocusControlRequest.ProtoReflect.Descriptor instead.
func (*FocusControlRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{49}
}

func (x *FocusControlRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *FocusControlRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *FocusControlRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *FocusControlRequest) GetSpeed() int32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type PresetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	PresetId      int32                  `protobuf:"varint,3,opt,name=presetId,proto3" json:"presetId,omitempty"` // 1-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetRequest) Reset() {
	*x = PresetRequest{}
	mi := &file_gb28181_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetRequest) ProtoMessage() {}

func (x *PresetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetRequest.ProtoReflect.Descriptor instead.
func (*PresetRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{50}
}

func (x *PresetRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *PresetRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *PresetRequest) GetPresetId() int32 {
	if x != nil {
		return x.PresetId
	}
	return 0
}

type PresetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []int32                `protobuf:"varint,3,rep,packed,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PresetResponse) Reset() {
	*x = PresetResponse{}
	mi := &file_gb28181_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PresetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PresetResponse) ProtoMessage() {}

func (x *PresetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PresetResponse.ProtoReflect.Descriptor instead.
func (*PresetResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{51}
}

func (x *PresetResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PresetResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PresetResponse) GetData() []int32 {
	if x != nil {
		return x.Data
	}
	return nil
}

type CruisePointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	CruiseId      int32                  `protobuf:"varint,3,opt,name=cruiseId,proto3" json:"cruiseId,omitempty"` // 0-255
	PresetId      int32                  `protobuf:"varint,4,opt,name=presetId,proto3" json:"presetId,omitempty"` // 1-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CruisePointRequest) Reset() {
	*x = CruisePointRequest{}
	mi := &file_gb28181_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CruisePointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruisePointRequest) ProtoMessage() {}

func (x *CruisePointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruisePointRequest.ProtoReflect.Descriptor instead.
func (*CruisePointRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{52}
}

func (x *CruisePointRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CruisePointRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *CruisePointRequest) GetCruiseId() int32 {
	if x != nil {
		return x.CruiseId
	}
	return 0
}

func (x *CruisePointRequest) GetPresetId() int32 {
	if x != nil {
		return x.PresetId
	}
	return 0
}

type CruiseSpeedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	CruiseId      int32                  `protobuf:"varint,3,opt,name=cruiseId,proto3" json:"cruiseId,omitempty"` // 0-255
	Speed         int32                  `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`       // 1-4095
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CruiseSpeedRequest) Reset() {
	*x = CruiseSpeedRequest{}
	mi := &file_gb28181_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CruiseSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruiseSpeedRequest) ProtoMessage() {}

func (x *CruiseSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruiseSpeedRequest.ProtoReflect.Descriptor instead.
func (*CruiseSpeedRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{53}
}

func (x *CruiseSpeedRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CruiseSpeedRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *CruiseSpeedRequest) GetCruiseId() int32 {
	if x != nil {
		return x.CruiseId
	}
	return 0
}

func (x *CruiseSpeedRequest) GetSpeed() int32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type CruiseTimeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	CruiseId      int32                  `protobuf:"varint,3,opt,name=cruiseId,proto3" json:"cruiseId,omitempty"` // 0-255
	Time          int32                  `protobuf:"varint,4,opt,name=time,proto3" json:"time,omitempty"`         // 1-4095
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CruiseTimeRequest) Reset() {
	*x = CruiseTimeRequest{}
	mi := &file_gb28181_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CruiseTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruiseTimeRequest) ProtoMessage() {}

func (x *CruiseTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruiseTimeRequest.ProtoReflect.Descriptor instead.
func (*CruiseTimeRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{54}
}

func (x *CruiseTimeRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CruiseTimeRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *CruiseTimeRequest) GetCruiseId() int32 {
	if x != nil {
		return x.CruiseId
	}
	return 0
}

func (x *CruiseTimeRequest) GetTime() int32 {
	if x != nil {
		return x.Time
	}
	return 0
}

type CruiseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	CruiseId      int32                  `protobuf:"varint,3,opt,name=cruiseId,proto3" json:"cruiseId,omitempty"` // 0-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CruiseRequest) Reset() {
	*x = CruiseRequest{}
	mi := &file_gb28181_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CruiseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CruiseRequest) ProtoMessage() {}

func (x *CruiseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CruiseRequest.ProtoReflect.Descriptor instead.
func (*CruiseRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{55}
}

func (x *CruiseRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *CruiseRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *CruiseRequest) GetCruiseId() int32 {
	if x != nil {
		return x.CruiseId
	}
	return 0
}

type ScanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ScanId        int32                  `protobuf:"varint,3,opt,name=scanId,proto3" json:"scanId,omitempty"` // 0-255
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanRequest) Reset() {
	*x = ScanRequest{}
	mi := &file_gb28181_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanRequest) ProtoMessage() {}

func (x *ScanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanRequest.ProtoReflect.Descriptor instead.
func (*ScanRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{56}
}

func (x *ScanRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ScanRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ScanRequest) GetScanId() int32 {
	if x != nil {
		return x.ScanId
	}
	return 0
}

type ScanSpeedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	ScanId        int32                  `protobuf:"varint,3,opt,name=scanId,proto3" json:"scanId,omitempty"` // 0-255
	Speed         int32                  `protobuf:"varint,4,opt,name=speed,proto3" json:"speed,omitempty"`   // 1-4095
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScanSpeedRequest) Reset() {
	*x = ScanSpeedRequest{}
	mi := &file_gb28181_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScanSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScanSpeedRequest) ProtoMessage() {}

func (x *ScanSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScanSpeedRequest.ProtoReflect.Descriptor instead.
func (*ScanSpeedRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{57}
}

func (x *ScanSpeedRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *ScanSpeedRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ScanSpeedRequest) GetScanId() int32 {
	if x != nil {
		return x.ScanId
	}
	return 0
}

func (x *ScanSpeedRequest) GetSpeed() int32 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type WiperControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"` // on, off
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WiperControlRequest) Reset() {
	*x = WiperControlRequest{}
	mi := &file_gb28181_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WiperControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WiperControlRequest) ProtoMessage() {}

func (x *WiperControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WiperControlRequest.ProtoReflect.Descriptor instead.
func (*WiperControlRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{58}
}

func (x *WiperControlRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *WiperControlRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *WiperControlRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

type AuxiliaryControlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"`
	ChannelId     string                 `protobuf:"bytes,2,opt,name=channelId,proto3" json:"channelId,omitempty"`
	Command       string                 `protobuf:"bytes,3,opt,name=command,proto3" json:"command,omitempty"` // on, off
	SwitchId      int32                  `protobuf:"varint,4,opt,name=switchId,proto3" json:"switchId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuxiliaryControlRequest) Reset() {
	*x = AuxiliaryControlRequest{}
	mi := &file_gb28181_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuxiliaryControlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuxiliaryControlRequest) ProtoMessage() {}

func (x *AuxiliaryControlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuxiliaryControlRequest.ProtoReflect.Descriptor instead.
func (*AuxiliaryControlRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{59}
}

func (x *AuxiliaryControlRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AuxiliaryControlRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *AuxiliaryControlRequest) GetCommand() string {
	if x != nil {
		return x.Command
	}
	return ""
}

func (x *AuxiliaryControlRequest) GetSwitchId() int32 {
	if x != nil {
		return x.SwitchId
	}
	return 0
}

// 测试SIP连接的消息定义
type TestSipRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TestParam     string                 `protobuf:"bytes,1,opt,name=testParam,proto3" json:"testParam,omitempty"` // 测试参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestSipRequest) Reset() {
	*x = TestSipRequest{}
	mi := &file_gb28181_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestSipRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestSipRequest) ProtoMessage() {}

func (x *TestSipRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestSipRequest.ProtoReflect.Descriptor instead.
func (*TestSipRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{60}
}

func (x *TestSipRequest) GetTestParam() string {
	if x != nil {
		return x.TestParam
	}
	return ""
}

type TestSipResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`            // 响应代码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`       // 响应消息
	TestResult    string                 `protobuf:"bytes,3,opt,name=testResult,proto3" json:"testResult,omitempty"` // 测试结果
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TestSipResponse) Reset() {
	*x = TestSipResponse{}
	mi := &file_gb28181_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TestSipResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TestSipResponse) ProtoMessage() {}

func (x *TestSipResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TestSipResponse.ProtoReflect.Descriptor instead.
func (*TestSipResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{61}
}

func (x *TestSipResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *TestSipResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *TestSipResponse) GetTestResult() string {
	if x != nil {
		return x.TestResult
	}
	return ""
}

// 报警记录查询请求
type SearchAlarmsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceId      string                 `protobuf:"bytes,1,opt,name=deviceId,proto3" json:"deviceId,omitempty"` // 设备ID，可选
	Start         string                 `protobuf:"bytes,2,opt,name=start,proto3" json:"start,omitempty"`       // 开始时间
	End           string                 `protobuf:"bytes,3,opt,name=end,proto3" json:"end,omitempty"`           // 结束时间
	Range         string                 `protobuf:"bytes,4,opt,name=range,proto3" json:"range,omitempty"`       // 时间范围，可选
	Page          int32                  `protobuf:"varint,5,opt,name=page,proto3" json:"page,omitempty"`        // 页码，从1开始
	Count         int32                  `protobuf:"varint,6,opt,name=count,proto3" json:"count,omitempty"`      // 每页大小
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAlarmsRequest) Reset() {
	*x = SearchAlarmsRequest{}
	mi := &file_gb28181_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAlarmsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAlarmsRequest) ProtoMessage() {}

func (x *SearchAlarmsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAlarmsRequest.ProtoReflect.Descriptor instead.
func (*SearchAlarmsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{62}
}

func (x *SearchAlarmsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *SearchAlarmsRequest) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *SearchAlarmsRequest) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *SearchAlarmsRequest) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *SearchAlarmsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *SearchAlarmsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

// 报警记录查询响应
type SearchAlarmsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`      // 响应代码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"` // 响应消息
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`    // 总记录数
	Data          []*AlarmRecord         `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`       // 报警记录列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchAlarmsResponse) Reset() {
	*x = SearchAlarmsResponse{}
	mi := &file_gb28181_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchAlarmsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchAlarmsResponse) ProtoMessage() {}

func (x *SearchAlarmsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchAlarmsResponse.ProtoReflect.Descriptor instead.
func (*SearchAlarmsResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{63}
}

func (x *SearchAlarmsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SearchAlarmsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *SearchAlarmsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *SearchAlarmsResponse) GetData() []*AlarmRecord {
	if x != nil {
		return x.Data
	}
	return nil
}

// 报警记录详情
type AlarmRecord struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Id                string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                                // 报警记录ID
	DeviceId          string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`                    // 设备ID
	DeviceName        string                 `protobuf:"bytes,3,opt,name=deviceName,proto3" json:"deviceName,omitempty"`                // 设备名称
	ChannelId         string                 `protobuf:"bytes,4,opt,name=channelId,proto3" json:"channelId,omitempty"`                  // 通道ID
	AlarmPriority     string                 `protobuf:"bytes,5,opt,name=alarmPriority,proto3" json:"alarmPriority,omitempty"`          // 报警级别
	AlarmMethod       string                 `protobuf:"bytes,6,opt,name=alarmMethod,proto3" json:"alarmMethod,omitempty"`              // 报警方式
	AlarmTime         *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=alarmTime,proto3" json:"alarmTime,omitempty"`                  // 报警时间
	AlarmDescription  string                 `protobuf:"bytes,8,opt,name=alarmDescription,proto3" json:"alarmDescription,omitempty"`    // 报警描述
	Longitude         float64                `protobuf:"fixed64,9,opt,name=longitude,proto3" json:"longitude,omitempty"`                // 经度
	Latitude          float64                `protobuf:"fixed64,10,opt,name=latitude,proto3" json:"latitude,omitempty"`                 // 纬度
	AlarmType         string                 `protobuf:"bytes,11,opt,name=alarmType,proto3" json:"alarmType,omitempty"`                 // 报警类型
	CreateTime        *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=createTime,proto3" json:"createTime,omitempty"`               // 创建时间
	AlarmPriorityDesc string                 `protobuf:"bytes,13,opt,name=alarmPriorityDesc,proto3" json:"alarmPriorityDesc,omitempty"` // 报警级别描述
	AlarmMethodDesc   string                 `protobuf:"bytes,14,opt,name=alarmMethodDesc,proto3" json:"alarmMethodDesc,omitempty"`     // 报警方式描述
	AlarmTypeDesc     string                 `protobuf:"bytes,15,opt,name=alarmTypeDesc,proto3" json:"alarmTypeDesc,omitempty"`         // 报警类型描述
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AlarmRecord) Reset() {
	*x = AlarmRecord{}
	mi := &file_gb28181_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AlarmRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AlarmRecord) ProtoMessage() {}

func (x *AlarmRecord) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AlarmRecord.ProtoReflect.Descriptor instead.
func (*AlarmRecord) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{64}
}

func (x *AlarmRecord) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AlarmRecord) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *AlarmRecord) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *AlarmRecord) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *AlarmRecord) GetAlarmPriority() string {
	if x != nil {
		return x.AlarmPriority
	}
	return ""
}

func (x *AlarmRecord) GetAlarmMethod() string {
	if x != nil {
		return x.AlarmMethod
	}
	return ""
}

func (x *AlarmRecord) GetAlarmTime() *timestamppb.Timestamp {
	if x != nil {
		return x.AlarmTime
	}
	return nil
}

func (x *AlarmRecord) GetAlarmDescription() string {
	if x != nil {
		return x.AlarmDescription
	}
	return ""
}

func (x *AlarmRecord) GetLongitude() float64 {
	if x != nil {
		return x.Longitude
	}
	return 0
}

func (x *AlarmRecord) GetLatitude() float64 {
	if x != nil {
		return x.Latitude
	}
	return 0
}

func (x *AlarmRecord) GetAlarmType() string {
	if x != nil {
		return x.AlarmType
	}
	return ""
}

func (x *AlarmRecord) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AlarmRecord) GetAlarmPriorityDesc() string {
	if x != nil {
		return x.AlarmPriorityDesc
	}
	return ""
}

func (x *AlarmRecord) GetAlarmMethodDesc() string {
	if x != nil {
		return x.AlarmMethodDesc
	}
	return ""
}

func (x *AlarmRecord) GetAlarmTypeDesc() string {
	if x != nil {
		return x.AlarmTypeDesc
	}
	return ""
}

// 添加平台通道请求
type AddPlatformChannelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlatformId    string                 `protobuf:"bytes,1,opt,name=platformId,proto3" json:"platformId,omitempty"` // 平台ID
	ChannelIds    []string               `protobuf:"bytes,2,rep,name=channelIds,proto3" json:"channelIds,omitempty"` // 通道ID列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPlatformChannelRequest) Reset() {
	*x = AddPlatformChannelRequest{}
	mi := &file_gb28181_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPlatformChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPlatformChannelRequest) ProtoMessage() {}

func (x *AddPlatformChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPlatformChannelRequest.ProtoReflect.Descriptor instead.
func (*AddPlatformChannelRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{65}
}

func (x *AddPlatformChannelRequest) GetPlatformId() string {
	if x != nil {
		return x.PlatformId
	}
	return ""
}

func (x *AddPlatformChannelRequest) GetChannelIds() []string {
	if x != nil {
		return x.ChannelIds
	}
	return nil
}

// 录制控制请求
type RecordingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CmdType       string                 `protobuf:"bytes,1,opt,name=cmdType,proto3" json:"cmdType,omitempty"`     // 命令类型：start/stop
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID
	ChannelId     string                 `protobuf:"bytes,3,opt,name=channelId,proto3" json:"channelId,omitempty"` // 通道ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordingRequest) Reset() {
	*x = RecordingRequest{}
	mi := &file_gb28181_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordingRequest) ProtoMessage() {}

func (x *RecordingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordingRequest.ProtoReflect.Descriptor instead.
func (*RecordingRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{66}
}

func (x *RecordingRequest) GetCmdType() string {
	if x != nil {
		return x.CmdType
	}
	return ""
}

func (x *RecordingRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *RecordingRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

// JPEG文件上传请求
type UploadJpegRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImageData     []byte                 `protobuf:"bytes,1,opt,name=imageData,proto3" json:"imageData,omitempty"` // JPEG文件数据
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadJpegRequest) Reset() {
	*x = UploadJpegRequest{}
	mi := &file_gb28181_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadJpegRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadJpegRequest) ProtoMessage() {}

func (x *UploadJpegRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadJpegRequest.ProtoReflect.Descriptor instead.
func (*UploadJpegRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{67}
}

func (x *UploadJpegRequest) GetImageData() []byte {
	if x != nil {
		return x.ImageData
	}
	return nil
}

// 分组消息定义
type Group struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                // ID表示数据库中的唯一标识符
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=createTime,proto3" json:"createTime,omitempty"` // 创建时间
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=updateTime,proto3" json:"updateTime,omitempty"` // 更新时间
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`             // 分组名称
	Pid           int32                  `protobuf:"varint,5,opt,name=pid,proto3" json:"pid,omitempty"`              // 父分组ID
	Level         int32                  `protobuf:"varint,6,opt,name=level,proto3" json:"level,omitempty"`          // 分组层级
	Children      []*Group               `protobuf:"bytes,7,rep,name=children,proto3" json:"children,omitempty"`     // 子组织数组
	Channels      []*GroupChannel        `protobuf:"bytes,8,rep,name=channels,proto3" json:"channels,omitempty"`     // 该组关联的通道列表
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Group) Reset() {
	*x = Group{}
	mi := &file_gb28181_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Group) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Group) ProtoMessage() {}

func (x *Group) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Group.ProtoReflect.Descriptor instead.
func (*Group) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{68}
}

func (x *Group) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Group) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Group) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Group) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Group) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

func (x *Group) GetLevel() int32 {
	if x != nil {
		return x.Level
	}
	return 0
}

func (x *Group) GetChildren() []*Group {
	if x != nil {
		return x.Children
	}
	return nil
}

func (x *Group) GetChannels() []*GroupChannel {
	if x != nil {
		return x.Channels
	}
	return nil
}

// 获取单个分组请求
type GetGroupsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           int32                  `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupsRequest) Reset() {
	*x = GetGroupsRequest{}
	mi := &file_gb28181_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupsRequest) ProtoMessage() {}

func (x *GetGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupsRequest.ProtoReflect.Descriptor instead.
func (*GetGroupsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{69}
}

func (x *GetGroupsRequest) GetPid() int32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

// 删除分组请求
type DeleteGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteGroupRequest) Reset() {
	*x = DeleteGroupRequest{}
	mi := &file_gb28181_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupRequest) ProtoMessage() {}

func (x *DeleteGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteGroupRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{70}
}

func (x *DeleteGroupRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 分组响应
type GroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *Group                 `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupResponse) Reset() {
	*x = GroupResponse{}
	mi := &file_gb28181_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupResponse) ProtoMessage() {}

func (x *GroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupResponse.ProtoReflect.Descriptor instead.
func (*GroupResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{71}
}

func (x *GroupResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GroupResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GroupResponse) GetData() *Group {
	if x != nil {
		return x.Data
	}
	return nil
}

// 分组列表响应（无分页）
type GroupsListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          []*Group               `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupsListResponse) Reset() {
	*x = GroupsListResponse{}
	mi := &file_gb28181_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupsListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupsListResponse) ProtoMessage() {}

func (x *GroupsListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupsListResponse.ProtoReflect.Descriptor instead.
func (*GroupsListResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{72}
}

func (x *GroupsListResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GroupsListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GroupsListResponse) GetData() []*Group {
	if x != nil {
		return x.Data
	}
	return nil
}

// 分组分页信息
type GroupsPageInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Data          []*Group               `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupsPageInfo) Reset() {
	*x = GroupsPageInfo{}
	mi := &file_gb28181_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupsPageInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupsPageInfo) ProtoMessage() {}

func (x *GroupsPageInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupsPageInfo.ProtoReflect.Descriptor instead.
func (*GroupsPageInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{73}
}

func (x *GroupsPageInfo) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GroupsPageInfo) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GroupsPageInfo) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GroupsPageInfo) GetData() []*Group {
	if x != nil {
		return x.Data
	}
	return nil
}

// GroupChannel相关消息定义
// 通道信息
type ChannelInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChannelId     string                 `protobuf:"bytes,1,opt,name=channelId,proto3" json:"channelId,omitempty"` // 通道ID
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChannelInfo) Reset() {
	*x = ChannelInfo{}
	mi := &file_gb28181_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChannelInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChannelInfo) ProtoMessage() {}

func (x *ChannelInfo) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChannelInfo.ProtoReflect.Descriptor instead.
func (*ChannelInfo) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{74}
}

func (x *ChannelInfo) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *ChannelInfo) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 添加分组与通道关联请求
type AddGroupChannelRequest struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	GroupId       int32                             `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"`  // 分组ID，从URL路径中获取
	Channels      []*AddGroupChannelRequest_Channel `protobuf:"bytes,2,rep,name=channels,proto3" json:"channels,omitempty"` // 通道列表，支持批量添加
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddGroupChannelRequest) Reset() {
	*x = AddGroupChannelRequest{}
	mi := &file_gb28181_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddGroupChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGroupChannelRequest) ProtoMessage() {}

func (x *AddGroupChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGroupChannelRequest.ProtoReflect.Descriptor instead.
func (*AddGroupChannelRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{75}
}

func (x *AddGroupChannelRequest) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *AddGroupChannelRequest) GetChannels() []*AddGroupChannelRequest_Channel {
	if x != nil {
		return x.Channels
	}
	return nil
}

// 删除分组与通道关联请求
type DeleteGroupChannelRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`              // 关联记录ID，可选，在请求体中
	GroupId       int32                  `protobuf:"varint,2,opt,name=groupId,proto3" json:"groupId,omitempty"`    // 分组ID，从URL路径中获取
	ChannelId     string                 `protobuf:"bytes,3,opt,name=channelId,proto3" json:"channelId,omitempty"` // 通道ID，可选，在请求体中
	DeviceId      string                 `protobuf:"bytes,4,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID，可选，在请求体中
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteGroupChannelRequest) Reset() {
	*x = DeleteGroupChannelRequest{}
	mi := &file_gb28181_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteGroupChannelRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupChannelRequest) ProtoMessage() {}

func (x *DeleteGroupChannelRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupChannelRequest.ProtoReflect.Descriptor instead.
func (*DeleteGroupChannelRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{76}
}

func (x *DeleteGroupChannelRequest) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteGroupChannelRequest) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *DeleteGroupChannelRequest) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *DeleteGroupChannelRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 获取分组下的通道列表请求
type GetGroupChannelsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GroupId       int32                  `protobuf:"varint,1,opt,name=groupId,proto3" json:"groupId,omitempty"`  // 分组ID，从URL路径中获取
	Page          int32                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`        // 页码
	Count         int32                  `protobuf:"varint,3,opt,name=count,proto3" json:"count,omitempty"`      // 每页数量
	DeviceId      string                 `protobuf:"bytes,4,opt,name=deviceId,proto3" json:"deviceId,omitempty"` // 可选，按设备ID筛选
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupChannelsRequest) Reset() {
	*x = GetGroupChannelsRequest{}
	mi := &file_gb28181_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupChannelsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupChannelsRequest) ProtoMessage() {}

func (x *GetGroupChannelsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupChannelsRequest.ProtoReflect.Descriptor instead.
func (*GetGroupChannelsRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{77}
}

func (x *GetGroupChannelsRequest) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GetGroupChannelsRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetGroupChannelsRequest) GetCount() int32 {
	if x != nil {
		return x.Count
	}
	return 0
}

func (x *GetGroupChannelsRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

// 分组通道关联信息
type GroupChannel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int32                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                  // ID
	GroupId       int32                  `protobuf:"varint,4,opt,name=groupId,proto3" json:"groupId,omitempty"`        // 分组ID
	ChannelId     string                 `protobuf:"bytes,5,opt,name=channelId,proto3" json:"channelId,omitempty"`     // 通道ID
	DeviceId      string                 `protobuf:"bytes,6,opt,name=deviceId,proto3" json:"deviceId,omitempty"`       // 设备ID
	ChannelName   string                 `protobuf:"bytes,7,opt,name=channelName,proto3" json:"channelName,omitempty"` // 通道名称，非数据库字段，查询时填充
	DeviceName    string                 `protobuf:"bytes,8,opt,name=deviceName,proto3" json:"deviceName,omitempty"`   // 设备名称，非数据库字段，查询时填充
	Status        string                 `protobuf:"bytes,9,opt,name=status,proto3" json:"status,omitempty"`           // 通道状态，非数据库字段，查询时填充
	StreamMode    string                 `protobuf:"bytes,10,opt,name=streamMode,proto3" json:"streamMode,omitempty"`  // 传输协议(TCP-ACTIVE/TCP-PASSIVE/UDP)，非数据库字段，查询时从设备获取
	InGroup       bool                   `protobuf:"varint,11,opt,name=inGroup,proto3" json:"inGroup,omitempty"`       //是否加入组,true表示已经加入组,false表示未在组里
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupChannel) Reset() {
	*x = GroupChannel{}
	mi := &file_gb28181_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupChannel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupChannel) ProtoMessage() {}

func (x *GroupChannel) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupChannel.ProtoReflect.Descriptor instead.
func (*GroupChannel) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{78}
}

func (x *GroupChannel) GetId() int32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GroupChannel) GetGroupId() int32 {
	if x != nil {
		return x.GroupId
	}
	return 0
}

func (x *GroupChannel) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *GroupChannel) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *GroupChannel) GetChannelName() string {
	if x != nil {
		return x.ChannelName
	}
	return ""
}

func (x *GroupChannel) GetDeviceName() string {
	if x != nil {
		return x.DeviceName
	}
	return ""
}

func (x *GroupChannel) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GroupChannel) GetStreamMode() string {
	if x != nil {
		return x.StreamMode
	}
	return ""
}

func (x *GroupChannel) GetInGroup() bool {
	if x != nil {
		return x.InGroup
	}
	return false
}

// 分组通道列表响应
type GroupChannelsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Total         int32                  `protobuf:"varint,3,opt,name=total,proto3" json:"total,omitempty"`
	Data          []*GroupChannel        `protobuf:"bytes,4,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupChannelsResponse) Reset() {
	*x = GroupChannelsResponse{}
	mi := &file_gb28181_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupChannelsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupChannelsResponse) ProtoMessage() {}

func (x *GroupChannelsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupChannelsResponse.ProtoReflect.Descriptor instead.
func (*GroupChannelsResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{79}
}

func (x *GroupChannelsResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GroupChannelsResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *GroupChannelsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GroupChannelsResponse) GetData() []*GroupChannel {
	if x != nil {
		return x.Data
	}
	return nil
}

// 回放暂停请求
type PlaybackPauseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"` // 回放流路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaybackPauseRequest) Reset() {
	*x = PlaybackPauseRequest{}
	mi := &file_gb28181_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaybackPauseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackPauseRequest) ProtoMessage() {}

func (x *PlaybackPauseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackPauseRequest.ProtoReflect.Descriptor instead.
func (*PlaybackPauseRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{80}
}

func (x *PlaybackPauseRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

// 回放恢复请求
type PlaybackResumeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"` // 回放流路径
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaybackResumeRequest) Reset() {
	*x = PlaybackResumeRequest{}
	mi := &file_gb28181_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaybackResumeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackResumeRequest) ProtoMessage() {}

func (x *PlaybackResumeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackResumeRequest.ProtoReflect.Descriptor instead.
func (*PlaybackResumeRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{81}
}

func (x *PlaybackResumeRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

// 回放拖动播放请求
type PlaybackSeekRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"` // 回放流路径
	SeekTime      int64                  `protobuf:"varint,2,opt,name=seekTime,proto3" json:"seekTime,omitempty"`    // 拖动偏移量，单位s
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaybackSeekRequest) Reset() {
	*x = PlaybackSeekRequest{}
	mi := &file_gb28181_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaybackSeekRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackSeekRequest) ProtoMessage() {}

func (x *PlaybackSeekRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackSeekRequest.ProtoReflect.Descriptor instead.
func (*PlaybackSeekRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{82}
}

func (x *PlaybackSeekRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PlaybackSeekRequest) GetSeekTime() int64 {
	if x != nil {
		return x.SeekTime
	}
	return 0
}

// 回放倍速播放请求
type PlaybackSpeedRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"` // 回放流路径
	Speed         float64                `protobuf:"fixed64,2,opt,name=speed,proto3" json:"speed,omitempty"`         // 倍速0.25 0.5 1、2、4
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlaybackSpeedRequest) Reset() {
	*x = PlaybackSpeedRequest{}
	mi := &file_gb28181_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlaybackSpeedRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlaybackSpeedRequest) ProtoMessage() {}

func (x *PlaybackSpeedRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlaybackSpeedRequest.ProtoReflect.Descriptor instead.
func (*PlaybackSpeedRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{83}
}

func (x *PlaybackSpeedRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PlaybackSpeedRequest) GetSpeed() float64 {
	if x != nil {
		return x.Speed
	}
	return 0
}

type RemoveDeviceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RemoveDeviceRequest) Reset() {
	*x = RemoveDeviceRequest{}
	mi := &file_gb28181_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RemoveDeviceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RemoveDeviceRequest) ProtoMessage() {}

func (x *RemoveDeviceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RemoveDeviceRequest.ProtoReflect.Descriptor instead.
func (*RemoveDeviceRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{84}
}

func (x *RemoveDeviceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type OpenRTPServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Udp           bool                   `protobuf:"varint,3,opt,name=udp,proto3" json:"udp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OpenRTPServerRequest) Reset() {
	*x = OpenRTPServerRequest{}
	mi := &file_gb28181_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenRTPServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRTPServerRequest) ProtoMessage() {}

func (x *OpenRTPServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRTPServerRequest.ProtoReflect.Descriptor instead.
func (*OpenRTPServerRequest) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{85}
}

func (x *OpenRTPServerRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *OpenRTPServerRequest) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *OpenRTPServerRequest) GetUdp() bool {
	if x != nil {
		return x.Udp
	}
	return false
}

type OpenRTPServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          int32                  `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OpenRTPServerResponse) Reset() {
	*x = OpenRTPServerResponse{}
	mi := &file_gb28181_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OpenRTPServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OpenRTPServerResponse) ProtoMessage() {}

func (x *OpenRTPServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OpenRTPServerResponse.ProtoReflect.Descriptor instead.
func (*OpenRTPServerResponse) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{86}
}

func (x *OpenRTPServerResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *OpenRTPServerResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *OpenRTPServerResponse) GetData() int32 {
	if x != nil {
		return x.Data
	}
	return 0
}

type AddGroupChannelRequest_Channel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChannelId     string                 `protobuf:"bytes,1,opt,name=channelId,proto3" json:"channelId,omitempty"` // 通道ID
	DeviceId      string                 `protobuf:"bytes,2,opt,name=deviceId,proto3" json:"deviceId,omitempty"`   // 设备ID
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddGroupChannelRequest_Channel) Reset() {
	*x = AddGroupChannelRequest_Channel{}
	mi := &file_gb28181_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddGroupChannelRequest_Channel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddGroupChannelRequest_Channel) ProtoMessage() {}

func (x *AddGroupChannelRequest_Channel) ProtoReflect() protoreflect.Message {
	mi := &file_gb28181_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddGroupChannelRequest_Channel.ProtoReflect.Descriptor instead.
func (*AddGroupChannelRequest_Channel) Descriptor() ([]byte, []int) {
	return file_gb28181_proto_rawDescGZIP(), []int{75, 0}
}

func (x *AddGroupChannelRequest_Channel) GetChannelId() string {
	if x != nil {
		return x.ChannelId
	}
	return ""
}

func (x *AddGroupChannelRequest_Channel) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

var File_gb28181_proto protoreflect.FileDescriptor

const file_gb28181_proto_rawDesc = "" +
	"\n" +
	"\rgb28181.proto\x12\n" +
	"gb28181pro\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"<\n" +
	"\fBaseResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\".\n" +
	"\x10GetDeviceRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"k\n" +
	"\x11GetDevicesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12\x14\n" +
	"\x05query\x18\x03 \x01(\tR\x05query\x12\x16\n" +
	"\x06status\x18\x04 \x01(\bR\x06status\"}\n" +
	"\x0fDevicesPageInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12&\n" +
	"\x04data\x18\x04 \x03(\v2\x12.gb28181pro.DeviceR\x04data\"\xaa\x01\n" +
	"\x12GetChannelsRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12\x14\n" +
	"\x05query\x18\x04 \x01(\tR\x05query\x12\x16\n" +
	"\x06online\x18\x05 \x01(\bR\x06online\x12 \n" +
	"\vchannelType\x18\x06 \x01(\bR\vchannelType\"\x7f\n" +
	"\x10ChannelsPageInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12'\n" +
	"\x04list\x18\x04 \x03(\v2\x13.gb28181pro.ChannelR\x04list\"/\n" +
	"\x11SyncDeviceRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"\x86\x01\n" +
	"\n" +
	"SyncStatus\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12\x18\n" +
	"\acurrent\x18\x04 \x01(\x05R\acurrent\x12\x1a\n" +
	"\berrorMsg\x18\x05 \x01(\tR\berrorMsg\"1\n" +
	"\x13DeleteDeviceRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"`\n" +
	"\x14DeleteDeviceResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\bdeviceId\x18\x03 \x01(\tR\bdeviceId\"\xcb\x01\n" +
	"\x15GetSubChannelsRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x12\n" +
	"\x04page\x18\x03 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\x12\x14\n" +
	"\x05query\x18\x05 \x01(\tR\x05query\x12\x16\n" +
	"\x06online\x18\x06 \x01(\bR\x06online\x12 \n" +
	"\vchannelType\x18\a \x01(\bR\vchannelType\"\x9d\x04\n" +
	"\aChannel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x03 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bparentId\x18\x04 \x01(\tR\bparentId\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\"\n" +
	"\fmanufacturer\x18\x06 \x01(\tR\fmanufacturer\x12\x14\n" +
	"\x05model\x18\a \x01(\tR\x05model\x12\x14\n" +
	"\x05owner\x18\b \x01(\tR\x05owner\x12\x1c\n" +
	"\tcivilCode\x18\t \x01(\tR\tcivilCode\x12\x18\n" +
	"\aaddress\x18\n" +
	" \x01(\tR\aaddress\x12\x12\n" +
	"\x04port\x18\v \x01(\x05R\x04port\x12\x1a\n" +
	"\bparental\x18\f \x01(\x05R\bparental\x12\x1c\n" +
	"\tsafetyWay\x18\r \x01(\x05R\tsafetyWay\x12 \n" +
	"\vregisterWay\x18\x0e \x01(\x05R\vregisterWay\x12\x18\n" +
	"\asecrecy\x18\x0f \x01(\x05R\asecrecy\x12\x16\n" +
	"\x06status\x18\x10 \x01(\tR\x06status\x124\n" +
	"\agpsTime\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampR\agpsTime\x12\x1c\n" +
	"\tlongitude\x18\x12 \x01(\tR\tlongitude\x12\x1a\n" +
	"\blatitude\x18\x13 \x01(\tR\blatitude\"\xdd\x05\n" +
	"\x06Device\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\"\n" +
	"\fmanufacturer\x18\x03 \x01(\tR\fmanufacturer\x12\x14\n" +
	"\x05model\x18\x04 \x01(\tR\x05model\x12\x1c\n" +
	"\tlongitude\x18\x05 \x01(\tR\tlongitude\x12\x1a\n" +
	"\blatitude\x18\x06 \x01(\tR\blatitude\x12\x16\n" +
	"\x06status\x18\a \x01(\tR\x06status\x12\x18\n" +
	"\amediaIp\x18\b \x01(\tR\amediaIp\x12>\n" +
	"\fregisterTime\x18\t \x01(\v2\x1a.google.protobuf.TimestampR\fregisterTime\x12:\n" +
	"\n" +
	"updateTime\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\rkeepAliveTime\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\rkeepAliveTime\x12\"\n" +
	"\fchannelCount\x18\f \x01(\x05R\fchannelCount\x12\x16\n" +
	"\x06online\x18\r \x01(\bR\x06online\x12/\n" +
	"\bchannels\x18\x0e \x03(\v2\x13.gb28181pro.ChannelR\bchannels\x12\x14\n" +
	"\x05sipIp\x18\x0f \x01(\tR\x05sipIp\x12\x1e\n" +
	"\n" +
	"streamMode\x18\x10 \x01(\tR\n" +
	"streamMode\x12\x1a\n" +
	"\bpassword\x18\x11 \x01(\tR\bpassword\x12*\n" +
	"\x10subscribeCatalog\x18\x12 \x01(\bR\x10subscribeCatalog\x12,\n" +
	"\x11subscribePosition\x18\x13 \x01(\bR\x11subscribePosition\x12&\n" +
	"\x0esubscribeAlarm\x18\x14 \x01(\bR\x0esubscribeAlarm\"d\n" +
	"\fResponseList\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x03(\v2\x12.gb28181pro.DeviceR\x04data\"H\n" +
	"\x12ChangeAudioRequest\x12\x1c\n" +
	"\tchannelId\x18\x01 \x01(\x05R\tchannelId\x12\x14\n" +
	"\x05audio\x18\x02 \x01(\bR\x05audio\"T\n" +
	"\x16UpdateTransportRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1e\n" +
	"\n" +
	"streamMode\x18\x02 \x01(\tR\n" +
	"streamMode\"4\n" +
	"\x16GetDeviceStatusRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"\\\n" +
	"\x14DeviceStatusResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x16\n" +
	"\x06status\x18\x03 \x01(\tR\x06status\"\xf3\x01\n" +
	"\x15GetDeviceAlarmRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12$\n" +
	"\rstartPriority\x18\x02 \x01(\tR\rstartPriority\x12 \n" +
	"\vendPriority\x18\x03 \x01(\tR\vendPriority\x12 \n" +
	"\valarmMethod\x18\x04 \x01(\tR\valarmMethod\x12\x1c\n" +
	"\talarmType\x18\x05 \x01(\tR\talarmType\x12\x1c\n" +
	"\tstartTime\x18\x06 \x01(\tR\tstartTime\x12\x18\n" +
	"\aendTime\x18\a \x01(\tR\aendTime\"n\n" +
	"\x13DeviceAlarmResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12)\n" +
	"\x04data\x18\x03 \x03(\v2\x15.gb28181pro.AlarmInfoR\x04data\"\xb9\x01\n" +
	"\tAlarmInfo\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12$\n" +
	"\ralarmPriority\x18\x02 \x01(\tR\ralarmPriority\x12 \n" +
	"\valarmMethod\x18\x03 \x01(\tR\valarmMethod\x12\x1c\n" +
	"\talarmTime\x18\x04 \x01(\tR\talarmTime\x12*\n" +
	"\x10alarmDescription\x18\x05 \x01(\tR\x10alarmDescription\"2\n" +
	"\x14GetSyncStatusRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"5\n" +
	"\x17GetSubscribeInfoRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\"\xdb\x01\n" +
	"\x15SubscribeInfoResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12T\n" +
	"\vdialogState\x18\x03 \x03(\v22.gb28181pro.SubscribeInfoResponse.DialogStateEntryR\vdialogState\x1a>\n" +
	"\x10DialogStateEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\x05R\x05value:\x028\x01\"^\n" +
	"\x0eGetSnapRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x12\n" +
	"\x04mark\x18\x03 \x01(\tR\x04mark\"Z\n" +
	"\fSnapResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1c\n" +
	"\timageData\x18\x03 \x01(\fR\timageData\"&\n" +
	"\x14GetRawChannelRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"f\n" +
	"\x0eDeviceResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x01(\v2\x12.gb28181pro.DeviceR\x04data\"h\n" +
	"\x0fChannelResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12'\n" +
	"\x04data\x18\x03 \x01(\v2\x13.gb28181pro.ChannelR\x04data\"G\n" +
	"\vPlayRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\"\x89\x01\n" +
	"\x0fPlaybackRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x14\n" +
	"\x05start\x18\x03 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\tR\x03end\x12\x14\n" +
	"\x05range\x18\x05 \x01(\tR\x05range\"u\n" +
	"\fPlayResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x127\n" +
	"\vstream_info\x18\x03 \x01(\v2\x16.gb28181pro.StreamInfoR\n" +
	"streamInfo\"n\n" +
	"\n" +
	"StreamInfo\x12\x16\n" +
	"\x06stream\x18\x01 \x01(\tR\x06stream\x12\x10\n" +
	"\x03app\x18\x02 \x01(\tR\x03app\x12\x0e\n" +
	"\x02ip\x18\x03 \x01(\tR\x02ip\x12\x12\n" +
	"\x04port\x18\x04 \x01(\x05R\x04port\x12\x12\n" +
	"\x04ssrc\x18\x05 \x01(\tR\x04ssrc\"L\n" +
	"\x12ConvertStopRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12$\n" +
	"\rmediaServerId\x18\x02 \x01(\tR\rmediaServerId\"\x8c\x01\n" +
	"\x10BroadcastRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x18\n" +
	"\atimeout\x18\x03 \x01(\x05R\atimeout\x12$\n" +
	"\rbroadcastMode\x18\x04 \x01(\bR\rbroadcastMode\"o\n" +
	"\x11BroadcastResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1a\n" +
	"\bstreamId\x18\x03 \x01(\tR\bstreamId\x12\x10\n" +
	"\x03url\x18\x04 \x01(\tR\x03url\"t\n" +
	"\bSSRCInfo\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x12\n" +
	"\x04ssrc\x18\x03 \x01(\tR\x04ssrc\x12\x1a\n" +
	"\bstreamId\x18\x04 \x01(\tR\bstreamId\"\x80\x01\n" +
	"\x10SSRCListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04data\x18\x03 \x03(\v2\x14.gb28181pro.SSRCInfoR\x04data\x12\x14\n" +
	"\x05count\x18\x04 \x01(\x05R\x05count\"\xe4\t\n" +
	"\bPlatform\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x16\n" +
	"\x06enable\x18\x02 \x01(\bR\x06enable\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"serverGBId\x18\x04 \x01(\tR\n" +
	"serverGBId\x12&\n" +
	"\x0eserverGBDomain\x18\x05 \x01(\tR\x0eserverGBDomain\x12\x1a\n" +
	"\bserverIp\x18\x06 \x01(\tR\bserverIp\x12\x1e\n" +
	"\n" +
	"serverPort\x18\a \x01(\x05R\n" +
	"serverPort\x12\x1e\n" +
	"\n" +
	"deviceGBId\x18\b \x01(\tR\n" +
	"deviceGBId\x12\x1a\n" +
	"\bdeviceIp\x18\t \x01(\tR\bdeviceIp\x12\x1e\n" +
	"\n" +
	"devicePort\x18\n" +
	" \x01(\x05R\n" +
	"devicePort\x12\x1a\n" +
	"\busername\x18\v \x01(\tR\busername\x12\x1a\n" +
	"\bpassword\x18\f \x01(\tR\bpassword\x12\x18\n" +
	"\aexpires\x18\r \x01(\x05R\aexpires\x12 \n" +
	"\vkeepTimeout\x18\x0e \x01(\x05R\vkeepTimeout\x12\x1c\n" +
	"\ttransport\x18\x0f \x01(\tR\ttransport\x12\"\n" +
	"\fcharacterSet\x18\x10 \x01(\tR\fcharacterSet\x12\x10\n" +
	"\x03ptz\x18\x11 \x01(\bR\x03ptz\x12\x12\n" +
	"\x04rtcp\x18\x12 \x01(\bR\x04rtcp\x12\x16\n" +
	"\x06status\x18\x13 \x01(\bR\x06status\x12\"\n" +
	"\fchannelCount\x18\x14 \x01(\x05R\fchannelCount\x12*\n" +
	"\x10catalogSubscribe\x18\x15 \x01(\bR\x10catalogSubscribe\x12&\n" +
	"\x0ealarmSubscribe\x18\x16 \x01(\bR\x0ealarmSubscribe\x128\n" +
	"\x17mobilePositionSubscribe\x18\x17 \x01(\bR\x17mobilePositionSubscribe\x12\"\n" +
	"\fcatalogGroup\x18\x18 \x01(\x05R\fcatalogGroup\x12\x1e\n" +
	"\n" +
	"updateTime\x18\x19 \x01(\tR\n" +
	"updateTime\x12\x1e\n" +
	"\n" +
	"createTime\x18\x1a \x01(\tR\n" +
	"createTime\x12*\n" +
	"\x10asMessageChannel\x18\x1b \x01(\bR\x10asMessageChannel\x12\"\n" +
	"\fsendStreamIp\x18\x1c \x01(\tR\fsendStreamIp\x12(\n" +
	"\x0fautoPushChannel\x18\x1d \x01(\bR\x0fautoPushChannel\x120\n" +
	"\x13catalogWithPlatform\x18\x1e \x01(\x05R\x13catalogWithPlatform\x12*\n" +
	"\x10catalogWithGroup\x18\x1f \x01(\x05R\x10catalogWithGroup\x12,\n" +
	"\x11catalogWithRegion\x18  \x01(\x05R\x11catalogWithRegion\x12\x1c\n" +
	"\tcivilCode\x18! \x01(\tR\tcivilCode\x12\"\n" +
	"\fmanufacturer\x18\" \x01(\tR\fmanufacturer\x12\x14\n" +
	"\x05model\x18# \x01(\tR\x05model\x12\x18\n" +
	"\aaddress\x18$ \x01(\tR\aaddress\x12 \n" +
	"\vregisterWay\x18% \x01(\x05R\vregisterWay\x12\x18\n" +
	"\asecrecy\x18& \x01(\x05R\asecrecy\"$\n" +
	"\x12GetPlatformRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"'\n" +
	"\x15DeletePlatformRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"n\n" +
	"\x14ListPlatformsRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x02 \x01(\x05R\x05count\x12\x14\n" +
	"\x05query\x18\x03 \x01(\tR\x05query\x12\x16\n" +
	"\x06status\x18\x04 \x01(\bR\x06status\"j\n" +
	"\x10PlatformResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12(\n" +
	"\x04data\x18\x03 \x01(\v2\x14.gb28181pro.PlatformR\x04data\"\x81\x01\n" +
	"\x11PlatformsPageInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12(\n" +
	"\x04list\x18\x04 \x03(\v2\x14.gb28181pro.PlatformR\x04list\"\x8c\x01\n" +
	"\x12QueryRecordRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x14\n" +
	"\x05start\x18\x03 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\tR\x03end\x12\x14\n" +
	"\x05range\x18\x05 \x01(\tR\x05range\"\xb7\x02\n" +
	"\x13QueryRecordResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12*\n" +
	"\x04data\x18\x03 \x03(\v2\x16.gb28181pro.RecordItemR\x04data\x12\x1b\n" +
	"\tdevice_id\x18\x04 \x01(\tR\bdeviceId\x12\x1d\n" +
	"\n" +
	"channel_id\x18\x05 \x01(\tR\tchannelId\x12\x0e\n" +
	"\x02sn\x18\x06 \x01(\tR\x02sn\x12\x12\n" +
	"\x04name\x18\a \x01(\tR\x04name\x12\x17\n" +
	"\asum_num\x18\b \x01(\x05R\x06sumNum\x12\x14\n" +
	"\x05count\x18\t \x01(\x05R\x05count\x127\n" +
	"\tlast_time\x18\n" +
	" \x01(\v2\x1a.google.protobuf.TimestampR\blastTime\"\xf8\x01\n" +
	"\n" +
	"RecordItem\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bfilePath\x18\x03 \x01(\tR\bfilePath\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\x12\x1c\n" +
	"\tstartTime\x18\x05 \x01(\tR\tstartTime\x12\x18\n" +
	"\aendTime\x18\x06 \x01(\tR\aendTime\x12\x18\n" +
	"\asecrecy\x18\a \x01(\x05R\asecrecy\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\x12\x1e\n" +
	"\n" +
	"recorderId\x18\t \x01(\tR\n" +
	"recorderId\"e\n" +
	"\x11PtzControlRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x16\n" +
	"\x06ptzcmd\x18\x03 \x01(\tR\x06ptzcmd\"~\n" +
	"\x12IrisControlRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x14\n" +
	"\x05speed\x18\x04 \x01(\x05R\x05speed\"\x7f\n" +
	"\x13FocusControlRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x14\n" +
	"\x05speed\x18\x04 \x01(\x05R\x05speed\"e\n" +
	"\rPresetRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bpresetId\x18\x03 \x01(\x05R\bpresetId\"R\n" +
	"\x0ePresetResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x03(\x05R\x04data\"\x86\x01\n" +
	"\x12CruisePointRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bcruiseId\x18\x03 \x01(\x05R\bcruiseId\x12\x1a\n" +
	"\bpresetId\x18\x04 \x01(\x05R\bpresetId\"\x80\x01\n" +
	"\x12CruiseSpeedRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bcruiseId\x18\x03 \x01(\x05R\bcruiseId\x12\x14\n" +
	"\x05speed\x18\x04 \x01(\x05R\x05speed\"}\n" +
	"\x11CruiseTimeRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bcruiseId\x18\x03 \x01(\x05R\bcruiseId\x12\x12\n" +
	"\x04time\x18\x04 \x01(\x05R\x04time\"e\n" +
	"\rCruiseRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bcruiseId\x18\x03 \x01(\x05R\bcruiseId\"_\n" +
	"\vScanRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x16\n" +
	"\x06scanId\x18\x03 \x01(\x05R\x06scanId\"z\n" +
	"\x10ScanSpeedRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x16\n" +
	"\x06scanId\x18\x03 \x01(\x05R\x06scanId\x12\x14\n" +
	"\x05speed\x18\x04 \x01(\x05R\x05speed\"i\n" +
	"\x13WiperControlRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\"\x89\x01\n" +
	"\x17AuxiliaryControlRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x02 \x01(\tR\tchannelId\x12\x18\n" +
	"\acommand\x18\x03 \x01(\tR\acommand\x12\x1a\n" +
	"\bswitchId\x18\x04 \x01(\x05R\bswitchId\".\n" +
	"\x0eTestSipRequest\x12\x1c\n" +
	"\ttestParam\x18\x01 \x01(\tR\ttestParam\"_\n" +
	"\x0fTestSipResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"testResult\x18\x03 \x01(\tR\n" +
	"testResult\"\x99\x01\n" +
	"\x13SearchAlarmsRequest\x12\x1a\n" +
	"\bdeviceId\x18\x01 \x01(\tR\bdeviceId\x12\x14\n" +
	"\x05start\x18\x02 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x03 \x01(\tR\x03end\x12\x14\n" +
	"\x05range\x18\x04 \x01(\tR\x05range\x12\x12\n" +
	"\x04page\x18\x05 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x06 \x01(\x05R\x05count\"\x87\x01\n" +
	"\x14SearchAlarmsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12+\n" +
	"\x04data\x18\x04 \x03(\v2\x17.gb28181pro.AlarmRecordR\x04data\"\xb7\x04\n" +
	"\vAlarmRecord\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\x12\x1e\n" +
	"\n" +
	"deviceName\x18\x03 \x01(\tR\n" +
	"deviceName\x12\x1c\n" +
	"\tchannelId\x18\x04 \x01(\tR\tchannelId\x12$\n" +
	"\ralarmPriority\x18\x05 \x01(\tR\ralarmPriority\x12 \n" +
	"\valarmMethod\x18\x06 \x01(\tR\valarmMethod\x128\n" +
	"\talarmTime\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\talarmTime\x12*\n" +
	"\x10alarmDescription\x18\b \x01(\tR\x10alarmDescription\x12\x1c\n" +
	"\tlongitude\x18\t \x01(\x01R\tlongitude\x12\x1a\n" +
	"\blatitude\x18\n" +
	" \x01(\x01R\blatitude\x12\x1c\n" +
	"\talarmType\x18\v \x01(\tR\talarmType\x12:\n" +
	"\n" +
	"createTime\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12,\n" +
	"\x11alarmPriorityDesc\x18\r \x01(\tR\x11alarmPriorityDesc\x12(\n" +
	"\x0falarmMethodDesc\x18\x0e \x01(\tR\x0falarmMethodDesc\x12$\n" +
	"\ralarmTypeDesc\x18\x0f \x01(\tR\ralarmTypeDesc\"[\n" +
	"\x19AddPlatformChannelRequest\x12\x1e\n" +
	"\n" +
	"platformId\x18\x01 \x01(\tR\n" +
	"platformId\x12\x1e\n" +
	"\n" +
	"channelIds\x18\x02 \x03(\tR\n" +
	"channelIds\"f\n" +
	"\x10RecordingRequest\x12\x18\n" +
	"\acmdType\x18\x01 \x01(\tR\acmdType\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\x12\x1c\n" +
	"\tchannelId\x18\x03 \x01(\tR\tchannelId\"1\n" +
	"\x11UploadJpegRequest\x12\x1c\n" +
	"\timageData\x18\x01 \x01(\fR\timageData\"\xb0\x02\n" +
	"\x05Group\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12:\n" +
	"\n" +
	"createTime\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12:\n" +
	"\n" +
	"updateTime\x18\x03 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x10\n" +
	"\x03pid\x18\x05 \x01(\x05R\x03pid\x12\x14\n" +
	"\x05level\x18\x06 \x01(\x05R\x05level\x12-\n" +
	"\bchildren\x18\a \x03(\v2\x11.gb28181pro.GroupR\bchildren\x124\n" +
	"\bchannels\x18\b \x03(\v2\x18.gb28181pro.GroupChannelR\bchannels\"$\n" +
	"\x10GetGroupsRequest\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\x05R\x03pid\"$\n" +
	"\x12DeleteGroupRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\"d\n" +
	"\rGroupResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x04data\x18\x03 \x01(\v2\x11.gb28181pro.GroupR\x04data\"i\n" +
	"\x12GroupsListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12%\n" +
	"\x04data\x18\x03 \x03(\v2\x11.gb28181pro.GroupR\x04data\"{\n" +
	"\x0eGroupsPageInfo\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12%\n" +
	"\x04data\x18\x04 \x03(\v2\x11.gb28181pro.GroupR\x04data\"G\n" +
	"\vChannelInfo\x12\x1c\n" +
	"\tchannelId\x18\x01 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\"\xbf\x01\n" +
	"\x16AddGroupChannelRequest\x12\x18\n" +
	"\agroupId\x18\x01 \x01(\x05R\agroupId\x12F\n" +
	"\bchannels\x18\x02 \x03(\v2*.gb28181pro.AddGroupChannelRequest.ChannelR\bchannels\x1aC\n" +
	"\aChannel\x12\x1c\n" +
	"\tchannelId\x18\x01 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bdeviceId\x18\x02 \x01(\tR\bdeviceId\"\x7f\n" +
	"\x19DeleteGroupChannelRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x18\n" +
	"\agroupId\x18\x02 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tchannelId\x18\x03 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bdeviceId\x18\x04 \x01(\tR\bdeviceId\"y\n" +
	"\x17GetGroupChannelsRequest\x12\x18\n" +
	"\agroupId\x18\x01 \x01(\x05R\agroupId\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\x12\x14\n" +
	"\x05count\x18\x03 \x01(\x05R\x05count\x12\x1a\n" +
	"\bdeviceId\x18\x04 \x01(\tR\bdeviceId\"\x86\x02\n" +
	"\fGroupChannel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x05R\x02id\x12\x18\n" +
	"\agroupId\x18\x04 \x01(\x05R\agroupId\x12\x1c\n" +
	"\tchannelId\x18\x05 \x01(\tR\tchannelId\x12\x1a\n" +
	"\bdeviceId\x18\x06 \x01(\tR\bdeviceId\x12 \n" +
	"\vchannelName\x18\a \x01(\tR\vchannelName\x12\x1e\n" +
	"\n" +
	"deviceName\x18\b \x01(\tR\n" +
	"deviceName\x12\x16\n" +
	"\x06status\x18\t \x01(\tR\x06status\x12\x1e\n" +
	"\n" +
	"streamMode\x18\n" +
	" \x01(\tR\n" +
	"streamMode\x12\x18\n" +
	"\ainGroup\x18\v \x01(\bR\ainGroup\"\x89\x01\n" +
	"\x15GroupChannelsResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x14\n" +
	"\x05total\x18\x03 \x01(\x05R\x05total\x12,\n" +
	"\x04data\x18\x04 \x03(\v2\x18.gb28181pro.GroupChannelR\x04data\"6\n" +
	"\x14PlaybackPauseRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\"7\n" +
	"\x15PlaybackResumeRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\"Q\n" +
	"\x13PlaybackSeekRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1a\n" +
	"\bseekTime\x18\x02 \x01(\x03R\bseekTime\"L\n" +
	"\x14PlaybackSpeedRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05speed\x18\x02 \x01(\x01R\x05speed\"%\n" +
	"\x13RemoveDeviceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"\\\n" +
	"\x14OpenRTPServerRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x12\n" +
	"\x04port\x18\x02 \x01(\x05R\x04port\x12\x10\n" +
	"\x03udp\x18\x03 \x01(\bR\x03udp\"Y\n" +
	"\x15OpenRTPServerResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\x05R\x04data2\xfa>\n" +
	"\x03api\x12]\n" +
	"\x04List\x12\x1d.gb28181pro.GetDevicesRequest\x1a\x1b.gb28181pro.DevicesPageInfo\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/gb28181/api/list\x12n\n" +
	"\tGetDevice\x12\x1c.gb28181pro.GetDeviceRequest\x1a\x1a.gb28181pro.DeviceResponse\"'\x82\xd3\xe4\x93\x02!\x12\x1f/gb28181/api/devices/{deviceId}\x12f\n" +
	"\n" +
	"GetDevices\x12\x1d.gb28181pro.GetDevicesRequest\x1a\x1b.gb28181pro.DevicesPageInfo\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/gb28181/api/devices\x12}\n" +
	"\vGetChannels\x12\x1e.gb28181pro.GetChannelsRequest\x1a\x1c.gb28181pro.ChannelsPageInfo\"0\x82\xd3\xe4\x93\x02*\x12(/gb28181/api/devices/{deviceId}/channels\x12q\n" +
	"\n" +
	"SyncDevice\x12\x1d.gb28181pro.SyncDeviceRequest\x1a\x16.gb28181pro.SyncStatus\",\x82\xd3\xe4\x93\x02&\x12$/gb28181/api/devices/{deviceId}/sync\x12\x81\x01\n" +
	"\fDeleteDevice\x12\x1f.gb28181pro.DeleteDeviceRequest\x1a .gb28181pro.DeleteDeviceResponse\".\x82\xd3\xe4\x93\x02(*&/gb28181/api/devices/{deviceId}/delete\x12\x94\x01\n" +
	"\x0eGetSubChannels\x12!.gb28181pro.GetSubChannelsRequest\x1a\x1c.gb28181pro.ChannelsPageInfo\"A\x82\xd3\xe4\x93\x02;\x129/gb28181/api/sub_channels/{deviceId}/{channelId}/channels\x12n\n" +
	"\vChangeAudio\x12\x1e.gb28181pro.ChangeAudioRequest\x1a\x18.gb28181pro.BaseResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/gb28181/api/channel/audio\x12\x90\x01\n" +
	"!UpdateChannelStreamIdentification\x12\x13.gb28181pro.Channel\x1a\x18.gb28181pro.BaseResponse\"<\x82\xd3\xe4\x93\x026:\x01*\"1/gb28181/api/channel/stream/identification/update\x12\x87\x01\n" +
	"\x0fUpdateTransport\x12\".gb28181pro.UpdateTransportRequest\x1a\x18.gb28181pro.BaseResponse\"6\x82\xd3\xe4\x93\x020\"./gb28181/api/transport/{deviceId}/{streamMode}\x12]\n" +
	"\tAddDevice\x12\x12.gb28181pro.Device\x1a\x18.gb28181pro.BaseResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/gb28181/api/device/add\x12c\n" +
	"\fUpdateDevice\x12\x12.gb28181pro.Device\x1a\x18.gb28181pro.BaseResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/gb28181/api/device/update\x12\x87\x01\n" +
	"\x0fGetDeviceStatus\x12\".gb28181pro.GetDeviceStatusRequest\x1a .gb28181pro.DeviceStatusResponse\".\x82\xd3\xe4\x93\x02(\x12&/gb28181/api/devices/{deviceId}/status\x12{\n" +
	"\x0eGetDeviceAlarm\x12!.gb28181pro.GetDeviceAlarmRequest\x1a\x1f.gb28181pro.DeviceAlarmResponse\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/gb28181/api/alarm/{deviceId}\x12v\n" +
	"\rGetSyncStatus\x12 .gb28181pro.GetSyncStatusRequest\x1a\x16.gb28181pro.SyncStatus\"+\x82\xd3\xe4\x93\x02%\x12#/gb28181/api/{deviceId}/sync_status\x12\x8a\x01\n" +
	"\x10GetSubscribeInfo\x12#.gb28181pro.GetSubscribeInfoRequest\x1a!.gb28181pro.SubscribeInfoResponse\".\x82\xd3\xe4\x93\x02(\x12&/gb28181/api/{deviceId}/subscribe_info\x12q\n" +
	"\aGetSnap\x12\x1a.gb28181pro.GetSnapRequest\x1a\x18.gb28181pro.SnapResponse\"0\x82\xd3\xe4\x93\x02*\x12(/gb28181/api/snap/{deviceId}/{channelId}\x12t\n" +
	"\vStopConvert\x12\x1e.gb28181pro.ConvertStopRequest\x1a\x18.gb28181pro.BaseResponse\"+\x82\xd3\xe4\x93\x02%\"#/gb28181/api/play/convertStop/{key}\x12\x89\x01\n" +
	"\x0eStartBroadcast\x12\x1c.gb28181pro.BroadcastRequest\x1a\x1d.gb28181pro.BroadcastResponse\":\x82\xd3\xe4\x93\x024\"2/gb28181/api/play/broadcast/{deviceId}/{channelId}\x12\x88\x01\n" +
	"\rStopBroadcast\x12\x1c.gb28181pro.BroadcastRequest\x1a\x18.gb28181pro.BaseResponse\"?\x82\xd3\xe4\x93\x029\"7/gb28181/api/play/broadcast/stop/{deviceId}/{channelId}\x12b\n" +
	"\n" +
	"GetAllSSRC\x12\x16.google.protobuf.Empty\x1a\x1c.gb28181pro.SSRCListResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/gb28181/api/play/ssrc\x12h\n" +
	"\rGetRawChannel\x12 .gb28181pro.GetRawChannelRequest\x1a\x13.gb28181pro.Channel\" \x82\xd3\xe4\x93\x02\x1a\x12\x18/gb28181/api/channel/raw\x12c\n" +
	"\vAddPlatform\x12\x14.gb28181pro.Platform\x1a\x18.gb28181pro.BaseResponse\"$\x82\xd3\xe4\x93\x02\x1e:\x01*\"\x19/gb28181/api/platform/add\x12o\n" +
	"\vGetPlatform\x12\x1e.gb28181pro.GetPlatformRequest\x1a\x1c.gb28181pro.PlatformResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/gb28181/api/platform/{id}\x12i\n" +
	"\x0eUpdatePlatform\x12\x14.gb28181pro.Platform\x1a\x18.gb28181pro.BaseResponse\"'\x82\xd3\xe4\x93\x02!:\x01*\"\x1c/gb28181/api/platform/update\x12q\n" +
	"\x0eDeletePlatform\x12!.gb28181pro.DeletePlatformRequest\x1a\x18.gb28181pro.BaseResponse\"\"\x82\xd3\xe4\x93\x02\x1c*\x1a/gb28181/api/platform/{id}\x12t\n" +
	"\rListPlatforms\x12 .gb28181pro.ListPlatformsRequest\x1a\x1d.gb28181pro.PlatformsPageInfo\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/gb28181/api/platform/list\x12\x83\x01\n" +
	"\vQueryRecord\x12\x1e.gb28181pro.QueryRecordRequest\x1a\x1f.gb28181pro.QueryRecordResponse\"3\x82\xd3\xe4\x93\x02-\x12+/gb28181/api/records/{deviceId}/{channelId}\x12v\n" +
	"\n" +
	"PtzControl\x12\x1d.gb28181pro.PtzControlRequest\x1a\x18.gb28181pro.BaseResponse\"/\x82\xd3\xe4\x93\x02)\x12'/gb28181/api/ptz/{deviceId}/{channelId}\x12|\n" +
	"\vIrisControl\x12\x1e.gb28181pro.IrisControlRequest\x1a\x18.gb28181pro.BaseResponse\"3\x82\xd3\xe4\x93\x02-\x12+/gb28181/api/fi/iris/{deviceId}/{channelId}\x12\x7f\n" +
	"\fFocusControl\x12\x1f.gb28181pro.FocusControlRequest\x1a\x18.gb28181pro.BaseResponse\"4\x82\xd3\xe4\x93\x02.\x12,/gb28181/api/fi/focus/{deviceId}/{channelId}\x12~\n" +
	"\vQueryPreset\x12\x19.gb28181pro.PresetRequest\x1a\x1a.gb28181pro.PresetResponse\"8\x82\xd3\xe4\x93\x022\x120/gb28181/api/preset/query/{deviceId}/{channelId}\x12t\n" +
	"\tAddPreset\x12\x19.gb28181pro.PresetRequest\x1a\x18.gb28181pro.BaseResponse\"2\x82\xd3\xe4\x93\x02,\x12*/gb28181/api/preset/{deviceId}/{channelId}\x12z\n" +
	"\n" +
	"CallPreset\x12\x19.gb28181pro.PresetRequest\x1a\x18.gb28181pro.BaseResponse\"7\x82\xd3\xe4\x93\x021\x12//gb28181/api/preset/call/{deviceId}/{channelId}\x12~\n" +
	"\fDeletePreset\x12\x19.gb28181pro.PresetRequest\x1a\x18.gb28181pro.BaseResponse\"9\x82\xd3\xe4\x93\x023\x121/gb28181/api/preset/delete/{deviceId}/{channelId}\x12\x88\x01\n" +
	"\x0eAddCruisePoint\x12\x1e.gb28181pro.CruisePointRequest\x1a\x18.gb28181pro.BaseResponse\"<\x82\xd3\xe4\x93\x026\x124/gb28181/api/cruise/point/add/{deviceId}/{channelId}\x12\x8e\x01\n" +
	"\x11DeleteCruisePoint\x12\x1e.gb28181pro.CruisePointRequest\x1a\x18.gb28181pro.BaseResponse\"?\x82\xd3\xe4\x93\x029\x127/gb28181/api/cruise/point/delete/{deviceId}/{channelId}\x12\x84\x01\n" +
	"\x0eSetCruiseSpeed\x12\x1e.gb28181pro.CruiseSpeedRequest\x1a\x18.gb28181pro.BaseResponse\"8\x82\xd3\xe4\x93\x022\x120/gb28181/api/cruise/speed/{deviceId}/{channelId}\x12\x81\x01\n" +
	"\rSetCruiseTime\x12\x1d.gb28181pro.CruiseTimeRequest\x1a\x18.gb28181pro.BaseResponse\"7\x82\xd3\xe4\x93\x021\x12//gb28181/api/cruise/time/{deviceId}/{channelId}\x12|\n" +
	"\vStartCruise\x12\x19.gb28181pro.CruiseRequest\x1a\x18.gb28181pro.BaseResponse\"8\x82\xd3\xe4\x93\x022\x120/gb28181/api/cruise/start/{deviceId}/{channelId}\x12z\n" +
	"\n" +
	"StopCruise\x12\x19.gb28181pro.CruiseRequest\x1a\x18.gb28181pro.BaseResponse\"7\x82\xd3\xe4\x93\x021\x12//gb28181/api/cruise/stop/{deviceId}/{channelId}\x12v\n" +
	"\tStartScan\x12\x17.gb28181pro.ScanRequest\x1a\x18.gb28181pro.BaseResponse\"6\x82\xd3\xe4\x93\x020\x12./gb28181/api/scan/start/{deviceId}/{channelId}\x12t\n" +
	"\bStopScan\x12\x17.gb28181pro.ScanRequest\x1a\x18.gb28181pro.BaseResponse\"5\x82\xd3\xe4\x93\x02/\x12-/gb28181/api/scan/stop/{deviceId}/{channelId}\x12{\n" +
	"\vSetScanLeft\x12\x17.gb28181pro.ScanRequest\x1a\x18.gb28181pro.BaseResponse\"9\x82\xd3\xe4\x93\x023\x121/gb28181/api/scan/set/left/{deviceId}/{channelId}\x12}\n" +
	"\fSetScanRight\x12\x17.gb28181pro.ScanRequest\x1a\x18.gb28181pro.BaseResponse\":\x82\xd3\xe4\x93\x024\x122/gb28181/api/scan/set/right/{deviceId}/{channelId}\x12\x82\x01\n" +
	"\fSetScanSpeed\x12\x1c.gb28181pro.ScanSpeedRequest\x1a\x18.gb28181pro.BaseResponse\":\x82\xd3\xe4\x93\x024\x122/gb28181/api/scan/set/speed/{deviceId}/{channelId}\x12|\n" +
	"\fWiperControl\x12\x1f.gb28181pro.WiperControlRequest\x1a\x18.gb28181pro.BaseResponse\"1\x82\xd3\xe4\x93\x02+\x12)/gb28181/api/wiper/{deviceId}/{channelId}\x12\x88\x01\n" +
	"\x10AuxiliaryControl\x12#.gb28181pro.AuxiliaryControlRequest\x1a\x18.gb28181pro.BaseResponse\"5\x82\xd3\xe4\x93\x02/\x12-/gb28181/api/auxiliary/{deviceId}/{channelId}\x12`\n" +
	"\aTestSip\x12\x1a.gb28181pro.TestSipRequest\x1a\x1b.gb28181pro.TestSipResponse\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/gb28181/api/testsip\x12y\n" +
	"\fSearchAlarms\x12\x1f.gb28181pro.SearchAlarmsRequest\x1a .gb28181pro.SearchAlarmsResponse\"&\x82\xd3\xe4\x93\x02 \x12\x1e/gb28181/api/alarms/{deviceId}\x12\x83\x01\n" +
	"\x12AddPlatformChannel\x12%.gb28181pro.AddPlatformChannelRequest\x1a\x18.gb28181pro.BaseResponse\",\x82\xd3\xe4\x93\x02&:\x01*\"!/gb28181/api/platform/channel/add\x12\x84\x01\n" +
	"\tRecording\x12\x1c.gb28181pro.RecordingRequest\x1a\x18.gb28181pro.BaseResponse\"?\x82\xd3\xe4\x93\x029\x127/gb28181/api/recording/{cmdType}/{deviceId}/{channelId}\x12j\n" +
	"\n" +
	"UploadJpeg\x12\x1d.gb28181pro.UploadJpegRequest\x1a\x18.gb28181pro.BaseResponse\"#\x82\xd3\xe4\x93\x02\x1d:\x01*\"\x18/gb28181/api/snap/upload\x12p\n" +
	"\rPlaybackPause\x12 .gb28181pro.PlaybackPauseRequest\x1a\x18.gb28181pro.BaseResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/gb28181/api/playback/pause\x12s\n" +
	"\x0ePlaybackResume\x12!.gb28181pro.PlaybackResumeRequest\x1a\x18.gb28181pro.BaseResponse\"$\x82\xd3\xe4\x93\x02\x1e\x12\x1c/gb28181/api/playback/resume\x12m\n" +
	"\fPlaybackSeek\x12\x1f.gb28181pro.PlaybackSeekRequest\x1a\x18.gb28181pro.BaseResponse\"\"\x82\xd3\xe4\x93\x02\x1c\x12\x1a/gb28181/api/playback/seek\x12p\n" +
	"\rPlaybackSpeed\x12 .gb28181pro.PlaybackSpeedRequest\x1a\x18.gb28181pro.BaseResponse\"#\x82\xd3\xe4\x93\x02\x1d\x12\x1b/gb28181/api/playback/speed\x12l\n" +
	"\tGetGroups\x12\x1c.gb28181pro.GetGroupsRequest\x1a\x1e.gb28181pro.GroupsListResponse\"!\x82\xd3\xe4\x93\x02\x1b\x12\x19/gb28181/api/groups/{pid}\x12[\n" +
	"\bAddGroup\x12\x11.gb28181pro.Group\x1a\x18.gb28181pro.BaseResponse\"\"\x82\xd3\xe4\x93\x02\x1c:\x01*\"\x17/gb28181/api/groups/add\x12a\n" +
	"\vUpdateGroup\x12\x11.gb28181pro.Group\x1a\x18.gb28181pro.BaseResponse\"%\x82\xd3\xe4\x93\x02\x1f:\x01*\"\x1a/gb28181/api/groups/update\x12p\n" +
	"\vDeleteGroup\x12\x1e.gb28181pro.DeleteGroupRequest\x1a\x18.gb28181pro.BaseResponse\"'\x82\xd3\xe4\x93\x02!\"\x1f/gb28181/api/groups/delete/{id}\x12\x8c\x01\n" +
	"\x0fAddGroupChannel\x12\".gb28181pro.AddGroupChannelRequest\x1a\x18.gb28181pro.BaseResponse\";\x82\xd3\xe4\x93\x025:\bchannels\")/gb28181/api/groups/channel/add/{groupId}\x12\x8e\x01\n" +
	"\x12DeleteGroupChannel\x12%.gb28181pro.DeleteGroupChannelRequest\x1a\x18.gb28181pro.BaseResponse\"7\x82\xd3\xe4\x93\x021:\x01*\",/gb28181/api/groups/channel/delete/{groupId}\x12\x8a\x01\n" +
	"\x10GetGroupChannels\x12#.gb28181pro.GetGroupChannelsRequest\x1a!.gb28181pro.GroupChannelsResponse\".\x82\xd3\xe4\x93\x02(\x12&/gb28181/api/groups/{groupId}/channels\x12r\n" +
	"\fRemoveDevice\x12\x1f.gb28181pro.RemoveDeviceRequest\x1a\x18.gb28181pro.BaseResponse\"'\x82\xd3\xe4\x93\x02!\"\x1f/gb28181/api/device/remove/{id}\x12s\n" +
	"\rOpenRTPServer\x12 .gb28181pro.OpenRTPServerRequest\x1a!.gb28181pro.OpenRTPServerResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\"\x15/gb28181/api/rtp/openB\x1fZ\x1dm7s.live/v5/plugin/gb28181/pbb\x06proto3"

var (
	file_gb28181_proto_rawDescOnce sync.Once
	file_gb28181_proto_rawDescData []byte
)

func file_gb28181_proto_rawDescGZIP() []byte {
	file_gb28181_proto_rawDescOnce.Do(func() {
		file_gb28181_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_gb28181_proto_rawDesc), len(file_gb28181_proto_rawDesc)))
	})
	return file_gb28181_proto_rawDescData
}

var file_gb28181_proto_msgTypes = make([]protoimpl.MessageInfo, 89)
var file_gb28181_proto_goTypes = []any{
	(*BaseResponse)(nil),                   // 0: gb28181pro.BaseResponse
	(*GetDeviceRequest)(nil),               // 1: gb28181pro.GetDeviceRequest
	(*GetDevicesRequest)(nil),              // 2: gb28181pro.GetDevicesRequest
	(*DevicesPageInfo)(nil),                // 3: gb28181pro.DevicesPageInfo
	(*GetChannelsRequest)(nil),             // 4: gb28181pro.GetChannelsRequest
	(*ChannelsPageInfo)(nil),               // 5: gb28181pro.ChannelsPageInfo
	(*SyncDeviceRequest)(nil),              // 6: gb28181pro.SyncDeviceRequest
	(*SyncStatus)(nil),                     // 7: gb28181pro.SyncStatus
	(*DeleteDeviceRequest)(nil),            // 8: gb28181pro.DeleteDeviceRequest
	(*DeleteDeviceResponse)(nil),           // 9: gb28181pro.DeleteDeviceResponse
	(*GetSubChannelsRequest)(nil),          // 10: gb28181pro.GetSubChannelsRequest
	(*Channel)(nil),                        // 11: gb28181pro.Channel
	(*Device)(nil),                         // 12: gb28181pro.Device
	(*ResponseList)(nil),                   // 13: gb28181pro.ResponseList
	(*ChangeAudioRequest)(nil),             // 14: gb28181pro.ChangeAudioRequest
	(*UpdateTransportRequest)(nil),         // 15: gb28181pro.UpdateTransportRequest
	(*GetDeviceStatusRequest)(nil),         // 16: gb28181pro.GetDeviceStatusRequest
	(*DeviceStatusResponse)(nil),           // 17: gb28181pro.DeviceStatusResponse
	(*GetDeviceAlarmRequest)(nil),          // 18: gb28181pro.GetDeviceAlarmRequest
	(*DeviceAlarmResponse)(nil),            // 19: gb28181pro.DeviceAlarmResponse
	(*AlarmInfo)(nil),                      // 20: gb28181pro.AlarmInfo
	(*GetSyncStatusRequest)(nil),           // 21: gb28181pro.GetSyncStatusRequest
	(*GetSubscribeInfoRequest)(nil),        // 22: gb28181pro.GetSubscribeInfoRequest
	(*SubscribeInfoResponse)(nil),          // 23: gb28181pro.SubscribeInfoResponse
	(*GetSnapRequest)(nil),                 // 24: gb28181pro.GetSnapRequest
	(*SnapResponse)(nil),                   // 25: gb28181pro.SnapResponse
	(*GetRawChannelRequest)(nil),           // 26: gb28181pro.GetRawChannelRequest
	(*DeviceResponse)(nil),                 // 27: gb28181pro.DeviceResponse
	(*ChannelResponse)(nil),                // 28: gb28181pro.ChannelResponse
	(*PlayRequest)(nil),                    // 29: gb28181pro.PlayRequest
	(*PlaybackRequest)(nil),                // 30: gb28181pro.PlaybackRequest
	(*PlayResponse)(nil),                   // 31: gb28181pro.PlayResponse
	(*StreamInfo)(nil),                     // 32: gb28181pro.StreamInfo
	(*ConvertStopRequest)(nil),             // 33: gb28181pro.ConvertStopRequest
	(*BroadcastRequest)(nil),               // 34: gb28181pro.BroadcastRequest
	(*BroadcastResponse)(nil),              // 35: gb28181pro.BroadcastResponse
	(*SSRCInfo)(nil),                       // 36: gb28181pro.SSRCInfo
	(*SSRCListResponse)(nil),               // 37: gb28181pro.SSRCListResponse
	(*Platform)(nil),                       // 38: gb28181pro.Platform
	(*GetPlatformRequest)(nil),             // 39: gb28181pro.GetPlatformRequest
	(*DeletePlatformRequest)(nil),          // 40: gb28181pro.DeletePlatformRequest
	(*ListPlatformsRequest)(nil),           // 41: gb28181pro.ListPlatformsRequest
	(*PlatformResponse)(nil),               // 42: gb28181pro.PlatformResponse
	(*PlatformsPageInfo)(nil),              // 43: gb28181pro.PlatformsPageInfo
	(*QueryRecordRequest)(nil),             // 44: gb28181pro.QueryRecordRequest
	(*QueryRecordResponse)(nil),            // 45: gb28181pro.QueryRecordResponse
	(*RecordItem)(nil),                     // 46: gb28181pro.RecordItem
	(*PtzControlRequest)(nil),              // 47: gb28181pro.PtzControlRequest
	(*IrisControlRequest)(nil),             // 48: gb28181pro.IrisControlRequest
	(*FocusControlRequest)(nil),            // 49: gb28181pro.FocusControlRequest
	(*PresetRequest)(nil),                  // 50: gb28181pro.PresetRequest
	(*PresetResponse)(nil),                 // 51: gb28181pro.PresetResponse
	(*CruisePointRequest)(nil),             // 52: gb28181pro.CruisePointRequest
	(*CruiseSpeedRequest)(nil),             // 53: gb28181pro.CruiseSpeedRequest
	(*CruiseTimeRequest)(nil),              // 54: gb28181pro.CruiseTimeRequest
	(*CruiseRequest)(nil),                  // 55: gb28181pro.CruiseRequest
	(*ScanRequest)(nil),                    // 56: gb28181pro.ScanRequest
	(*ScanSpeedRequest)(nil),               // 57: gb28181pro.ScanSpeedRequest
	(*WiperControlRequest)(nil),            // 58: gb28181pro.WiperControlRequest
	(*AuxiliaryControlRequest)(nil),        // 59: gb28181pro.AuxiliaryControlRequest
	(*TestSipRequest)(nil),                 // 60: gb28181pro.TestSipRequest
	(*TestSipResponse)(nil),                // 61: gb28181pro.TestSipResponse
	(*SearchAlarmsRequest)(nil),            // 62: gb28181pro.SearchAlarmsRequest
	(*SearchAlarmsResponse)(nil),           // 63: gb28181pro.SearchAlarmsResponse
	(*AlarmRecord)(nil),                    // 64: gb28181pro.AlarmRecord
	(*AddPlatformChannelRequest)(nil),      // 65: gb28181pro.AddPlatformChannelRequest
	(*RecordingRequest)(nil),               // 66: gb28181pro.RecordingRequest
	(*UploadJpegRequest)(nil),              // 67: gb28181pro.UploadJpegRequest
	(*Group)(nil),                          // 68: gb28181pro.Group
	(*GetGroupsRequest)(nil),               // 69: gb28181pro.GetGroupsRequest
	(*DeleteGroupRequest)(nil),             // 70: gb28181pro.DeleteGroupRequest
	(*GroupResponse)(nil),                  // 71: gb28181pro.GroupResponse
	(*GroupsListResponse)(nil),             // 72: gb28181pro.GroupsListResponse
	(*GroupsPageInfo)(nil),                 // 73: gb28181pro.GroupsPageInfo
	(*ChannelInfo)(nil),                    // 74: gb28181pro.ChannelInfo
	(*AddGroupChannelRequest)(nil),         // 75: gb28181pro.AddGroupChannelRequest
	(*DeleteGroupChannelRequest)(nil),      // 76: gb28181pro.DeleteGroupChannelRequest
	(*GetGroupChannelsRequest)(nil),        // 77: gb28181pro.GetGroupChannelsRequest
	(*GroupChannel)(nil),                   // 78: gb28181pro.GroupChannel
	(*GroupChannelsResponse)(nil),          // 79: gb28181pro.GroupChannelsResponse
	(*PlaybackPauseRequest)(nil),           // 80: gb28181pro.PlaybackPauseRequest
	(*PlaybackResumeRequest)(nil),          // 81: gb28181pro.PlaybackResumeRequest
	(*PlaybackSeekRequest)(nil),            // 82: gb28181pro.PlaybackSeekRequest
	(*PlaybackSpeedRequest)(nil),           // 83: gb28181pro.PlaybackSpeedRequest
	(*RemoveDeviceRequest)(nil),            // 84: gb28181pro.RemoveDeviceRequest
	(*OpenRTPServerRequest)(nil),           // 85: gb28181pro.OpenRTPServerRequest
	(*OpenRTPServerResponse)(nil),          // 86: gb28181pro.OpenRTPServerResponse
	nil,                                    // 87: gb28181pro.SubscribeInfoResponse.DialogStateEntry
	(*AddGroupChannelRequest_Channel)(nil), // 88: gb28181pro.AddGroupChannelRequest.Channel
	(*timestamppb.Timestamp)(nil),          // 89: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                  // 90: google.protobuf.Empty
}
var file_gb28181_proto_depIdxs = []int32{
	12, // 0: gb28181pro.DevicesPageInfo.data:type_name -> gb28181pro.Device
	11, // 1: gb28181pro.ChannelsPageInfo.list:type_name -> gb28181pro.Channel
	89, // 2: gb28181pro.Channel.gpsTime:type_name -> google.protobuf.Timestamp
	89, // 3: gb28181pro.Device.registerTime:type_name -> google.protobuf.Timestamp
	89, // 4: gb28181pro.Device.updateTime:type_name -> google.protobuf.Timestamp
	89, // 5: gb28181pro.Device.keepAliveTime:type_name -> google.protobuf.Timestamp
	11, // 6: gb28181pro.Device.channels:type_name -> gb28181pro.Channel
	12, // 7: gb28181pro.ResponseList.data:type_name -> gb28181pro.Device
	20, // 8: gb28181pro.DeviceAlarmResponse.data:type_name -> gb28181pro.AlarmInfo
	87, // 9: gb28181pro.SubscribeInfoResponse.dialogState:type_name -> gb28181pro.SubscribeInfoResponse.DialogStateEntry
	12, // 10: gb28181pro.DeviceResponse.data:type_name -> gb28181pro.Device
	11, // 11: gb28181pro.ChannelResponse.data:type_name -> gb28181pro.Channel
	32, // 12: gb28181pro.PlayResponse.stream_info:type_name -> gb28181pro.StreamInfo
	36, // 13: gb28181pro.SSRCListResponse.data:type_name -> gb28181pro.SSRCInfo
	38, // 14: gb28181pro.PlatformResponse.data:type_name -> gb28181pro.Platform
	38, // 15: gb28181pro.PlatformsPageInfo.list:type_name -> gb28181pro.Platform
	46, // 16: gb28181pro.QueryRecordResponse.data:type_name -> gb28181pro.RecordItem
	89, // 17: gb28181pro.QueryRecordResponse.last_time:type_name -> google.protobuf.Timestamp
	64, // 18: gb28181pro.SearchAlarmsResponse.data:type_name -> gb28181pro.AlarmRecord
	89, // 19: gb28181pro.AlarmRecord.alarmTime:type_name -> google.protobuf.Timestamp
	89, // 20: gb28181pro.AlarmRecord.createTime:type_name -> google.protobuf.Timestamp
	89, // 21: gb28181pro.Group.createTime:type_name -> google.protobuf.Timestamp
	89, // 22: gb28181pro.Group.updateTime:type_name -> google.protobuf.Timestamp
	68, // 23: gb28181pro.Group.children:type_name -> gb28181pro.Group
	78, // 24: gb28181pro.Group.channels:type_name -> gb28181pro.GroupChannel
	68, // 25: gb28181pro.GroupResponse.data:type_name -> gb28181pro.Group
	68, // 26: gb28181pro.GroupsListResponse.data:type_name -> gb28181pro.Group
	68, // 27: gb28181pro.GroupsPageInfo.data:type_name -> gb28181pro.Group
	88, // 28: gb28181pro.AddGroupChannelRequest.channels:type_name -> gb28181pro.AddGroupChannelRequest.Channel
	78, // 29: gb28181pro.GroupChannelsResponse.data:type_name -> gb28181pro.GroupChannel
	2,  // 30: gb28181pro.api.List:input_type -> gb28181pro.GetDevicesRequest
	1,  // 31: gb28181pro.api.GetDevice:input_type -> gb28181pro.GetDeviceRequest
	2,  // 32: gb28181pro.api.GetDevices:input_type -> gb28181pro.GetDevicesRequest
	4,  // 33: gb28181pro.api.GetChannels:input_type -> gb28181pro.GetChannelsRequest
	6,  // 34: gb28181pro.api.SyncDevice:input_type -> gb28181pro.SyncDeviceRequest
	8,  // 35: gb28181pro.api.DeleteDevice:input_type -> gb28181pro.DeleteDeviceRequest
	10, // 36: gb28181pro.api.GetSubChannels:input_type -> gb28181pro.GetSubChannelsRequest
	14, // 37: gb28181pro.api.ChangeAudio:input_type -> gb28181pro.ChangeAudioRequest
	11, // 38: gb28181pro.api.UpdateChannelStreamIdentification:input_type -> gb28181pro.Channel
	15, // 39: gb28181pro.api.UpdateTransport:input_type -> gb28181pro.UpdateTransportRequest
	12, // 40: gb28181pro.api.AddDevice:input_type -> gb28181pro.Device
	12, // 41: gb28181pro.api.UpdateDevice:input_type -> gb28181pro.Device
	16, // 42: gb28181pro.api.GetDeviceStatus:input_type -> gb28181pro.GetDeviceStatusRequest
	18, // 43: gb28181pro.api.GetDeviceAlarm:input_type -> gb28181pro.GetDeviceAlarmRequest
	21, // 44: gb28181pro.api.GetSyncStatus:input_type -> gb28181pro.GetSyncStatusRequest
	22, // 45: gb28181pro.api.GetSubscribeInfo:input_type -> gb28181pro.GetSubscribeInfoRequest
	24, // 46: gb28181pro.api.GetSnap:input_type -> gb28181pro.GetSnapRequest
	33, // 47: gb28181pro.api.StopConvert:input_type -> gb28181pro.ConvertStopRequest
	34, // 48: gb28181pro.api.StartBroadcast:input_type -> gb28181pro.BroadcastRequest
	34, // 49: gb28181pro.api.StopBroadcast:input_type -> gb28181pro.BroadcastRequest
	90, // 50: gb28181pro.api.GetAllSSRC:input_type -> google.protobuf.Empty
	26, // 51: gb28181pro.api.GetRawChannel:input_type -> gb28181pro.GetRawChannelRequest
	38, // 52: gb28181pro.api.AddPlatform:input_type -> gb28181pro.Platform
	39, // 53: gb28181pro.api.GetPlatform:input_type -> gb28181pro.GetPlatformRequest
	38, // 54: gb28181pro.api.UpdatePlatform:input_type -> gb28181pro.Platform
	40, // 55: gb28181pro.api.DeletePlatform:input_type -> gb28181pro.DeletePlatformRequest
	41, // 56: gb28181pro.api.ListPlatforms:input_type -> gb28181pro.ListPlatformsRequest
	44, // 57: gb28181pro.api.QueryRecord:input_type -> gb28181pro.QueryRecordRequest
	47, // 58: gb28181pro.api.PtzControl:input_type -> gb28181pro.PtzControlRequest
	48, // 59: gb28181pro.api.IrisControl:input_type -> gb28181pro.IrisControlRequest
	49, // 60: gb28181pro.api.FocusControl:input_type -> gb28181pro.FocusControlRequest
	50, // 61: gb28181pro.api.QueryPreset:input_type -> gb28181pro.PresetRequest
	50, // 62: gb28181pro.api.AddPreset:input_type -> gb28181pro.PresetRequest
	50, // 63: gb28181pro.api.CallPreset:input_type -> gb28181pro.PresetRequest
	50, // 64: gb28181pro.api.DeletePreset:input_type -> gb28181pro.PresetRequest
	52, // 65: gb28181pro.api.AddCruisePoint:input_type -> gb28181pro.CruisePointRequest
	52, // 66: gb28181pro.api.DeleteCruisePoint:input_type -> gb28181pro.CruisePointRequest
	53, // 67: gb28181pro.api.SetCruiseSpeed:input_type -> gb28181pro.CruiseSpeedRequest
	54, // 68: gb28181pro.api.SetCruiseTime:input_type -> gb28181pro.CruiseTimeRequest
	55, // 69: gb28181pro.api.StartCruise:input_type -> gb28181pro.CruiseRequest
	55, // 70: gb28181pro.api.StopCruise:input_type -> gb28181pro.CruiseRequest
	56, // 71: gb28181pro.api.StartScan:input_type -> gb28181pro.ScanRequest
	56, // 72: gb28181pro.api.StopScan:input_type -> gb28181pro.ScanRequest
	56, // 73: gb28181pro.api.SetScanLeft:input_type -> gb28181pro.ScanRequest
	56, // 74: gb28181pro.api.SetScanRight:input_type -> gb28181pro.ScanRequest
	57, // 75: gb28181pro.api.SetScanSpeed:input_type -> gb28181pro.ScanSpeedRequest
	58, // 76: gb28181pro.api.WiperControl:input_type -> gb28181pro.WiperControlRequest
	59, // 77: gb28181pro.api.AuxiliaryControl:input_type -> gb28181pro.AuxiliaryControlRequest
	60, // 78: gb28181pro.api.TestSip:input_type -> gb28181pro.TestSipRequest
	62, // 79: gb28181pro.api.SearchAlarms:input_type -> gb28181pro.SearchAlarmsRequest
	65, // 80: gb28181pro.api.AddPlatformChannel:input_type -> gb28181pro.AddPlatformChannelRequest
	66, // 81: gb28181pro.api.Recording:input_type -> gb28181pro.RecordingRequest
	67, // 82: gb28181pro.api.UploadJpeg:input_type -> gb28181pro.UploadJpegRequest
	80, // 83: gb28181pro.api.PlaybackPause:input_type -> gb28181pro.PlaybackPauseRequest
	81, // 84: gb28181pro.api.PlaybackResume:input_type -> gb28181pro.PlaybackResumeRequest
	82, // 85: gb28181pro.api.PlaybackSeek:input_type -> gb28181pro.PlaybackSeekRequest
	83, // 86: gb28181pro.api.PlaybackSpeed:input_type -> gb28181pro.PlaybackSpeedRequest
	69, // 87: gb28181pro.api.GetGroups:input_type -> gb28181pro.GetGroupsRequest
	68, // 88: gb28181pro.api.AddGroup:input_type -> gb28181pro.Group
	68, // 89: gb28181pro.api.UpdateGroup:input_type -> gb28181pro.Group
	70, // 90: gb28181pro.api.DeleteGroup:input_type -> gb28181pro.DeleteGroupRequest
	75, // 91: gb28181pro.api.AddGroupChannel:input_type -> gb28181pro.AddGroupChannelRequest
	76, // 92: gb28181pro.api.DeleteGroupChannel:input_type -> gb28181pro.DeleteGroupChannelRequest
	77, // 93: gb28181pro.api.GetGroupChannels:input_type -> gb28181pro.GetGroupChannelsRequest
	84, // 94: gb28181pro.api.RemoveDevice:input_type -> gb28181pro.RemoveDeviceRequest
	85, // 95: gb28181pro.api.OpenRTPServer:input_type -> gb28181pro.OpenRTPServerRequest
	3,  // 96: gb28181pro.api.List:output_type -> gb28181pro.DevicesPageInfo
	27, // 97: gb28181pro.api.GetDevice:output_type -> gb28181pro.DeviceResponse
	3,  // 98: gb28181pro.api.GetDevices:output_type -> gb28181pro.DevicesPageInfo
	5,  // 99: gb28181pro.api.GetChannels:output_type -> gb28181pro.ChannelsPageInfo
	7,  // 100: gb28181pro.api.SyncDevice:output_type -> gb28181pro.SyncStatus
	9,  // 101: gb28181pro.api.DeleteDevice:output_type -> gb28181pro.DeleteDeviceResponse
	5,  // 102: gb28181pro.api.GetSubChannels:output_type -> gb28181pro.ChannelsPageInfo
	0,  // 103: gb28181pro.api.ChangeAudio:output_type -> gb28181pro.BaseResponse
	0,  // 104: gb28181pro.api.UpdateChannelStreamIdentification:output_type -> gb28181pro.BaseResponse
	0,  // 105: gb28181pro.api.UpdateTransport:output_type -> gb28181pro.BaseResponse
	0,  // 106: gb28181pro.api.AddDevice:output_type -> gb28181pro.BaseResponse
	0,  // 107: gb28181pro.api.UpdateDevice:output_type -> gb28181pro.BaseResponse
	17, // 108: gb28181pro.api.GetDeviceStatus:output_type -> gb28181pro.DeviceStatusResponse
	19, // 109: gb28181pro.api.GetDeviceAlarm:output_type -> gb28181pro.DeviceAlarmResponse
	7,  // 110: gb28181pro.api.GetSyncStatus:output_type -> gb28181pro.SyncStatus
	23, // 111: gb28181pro.api.GetSubscribeInfo:output_type -> gb28181pro.SubscribeInfoResponse
	25, // 112: gb28181pro.api.GetSnap:output_type -> gb28181pro.SnapResponse
	0,  // 113: gb28181pro.api.StopConvert:output_type -> gb28181pro.BaseResponse
	35, // 114: gb28181pro.api.StartBroadcast:output_type -> gb28181pro.BroadcastResponse
	0,  // 115: gb28181pro.api.StopBroadcast:output_type -> gb28181pro.BaseResponse
	37, // 116: gb28181pro.api.GetAllSSRC:output_type -> gb28181pro.SSRCListResponse
	11, // 117: gb28181pro.api.GetRawChannel:output_type -> gb28181pro.Channel
	0,  // 118: gb28181pro.api.AddPlatform:output_type -> gb28181pro.BaseResponse
	42, // 119: gb28181pro.api.GetPlatform:output_type -> gb28181pro.PlatformResponse
	0,  // 120: gb28181pro.api.UpdatePlatform:output_type -> gb28181pro.BaseResponse
	0,  // 121: gb28181pro.api.DeletePlatform:output_type -> gb28181pro.BaseResponse
	43, // 122: gb28181pro.api.ListPlatforms:output_type -> gb28181pro.PlatformsPageInfo
	45, // 123: gb28181pro.api.QueryRecord:output_type -> gb28181pro.QueryRecordResponse
	0,  // 124: gb28181pro.api.PtzControl:output_type -> gb28181pro.BaseResponse
	0,  // 125: gb28181pro.api.IrisControl:output_type -> gb28181pro.BaseResponse
	0,  // 126: gb28181pro.api.FocusControl:output_type -> gb28181pro.BaseResponse
	51, // 127: gb28181pro.api.QueryPreset:output_type -> gb28181pro.PresetResponse
	0,  // 128: gb28181pro.api.AddPreset:output_type -> gb28181pro.BaseResponse
	0,  // 129: gb28181pro.api.CallPreset:output_type -> gb28181pro.BaseResponse
	0,  // 130: gb28181pro.api.DeletePreset:output_type -> gb28181pro.BaseResponse
	0,  // 131: gb28181pro.api.AddCruisePoint:output_type -> gb28181pro.BaseResponse
	0,  // 132: gb28181pro.api.DeleteCruisePoint:output_type -> gb28181pro.BaseResponse
	0,  // 133: gb28181pro.api.SetCruiseSpeed:output_type -> gb28181pro.BaseResponse
	0,  // 134: gb28181pro.api.SetCruiseTime:output_type -> gb28181pro.BaseResponse
	0,  // 135: gb28181pro.api.StartCruise:output_type -> gb28181pro.BaseResponse
	0,  // 136: gb28181pro.api.StopCruise:output_type -> gb28181pro.BaseResponse
	0,  // 137: gb28181pro.api.StartScan:output_type -> gb28181pro.BaseResponse
	0,  // 138: gb28181pro.api.StopScan:output_type -> gb28181pro.BaseResponse
	0,  // 139: gb28181pro.api.SetScanLeft:output_type -> gb28181pro.BaseResponse
	0,  // 140: gb28181pro.api.SetScanRight:output_type -> gb28181pro.BaseResponse
	0,  // 141: gb28181pro.api.SetScanSpeed:output_type -> gb28181pro.BaseResponse
	0,  // 142: gb28181pro.api.WiperControl:output_type -> gb28181pro.BaseResponse
	0,  // 143: gb28181pro.api.AuxiliaryControl:output_type -> gb28181pro.BaseResponse
	61, // 144: gb28181pro.api.TestSip:output_type -> gb28181pro.TestSipResponse
	63, // 145: gb28181pro.api.SearchAlarms:output_type -> gb28181pro.SearchAlarmsResponse
	0,  // 146: gb28181pro.api.AddPlatformChannel:output_type -> gb28181pro.BaseResponse
	0,  // 147: gb28181pro.api.Recording:output_type -> gb28181pro.BaseResponse
	0,  // 148: gb28181pro.api.UploadJpeg:output_type -> gb28181pro.BaseResponse
	0,  // 149: gb28181pro.api.PlaybackPause:output_type -> gb28181pro.BaseResponse
	0,  // 150: gb28181pro.api.PlaybackResume:output_type -> gb28181pro.BaseResponse
	0,  // 151: gb28181pro.api.PlaybackSeek:output_type -> gb28181pro.BaseResponse
	0,  // 152: gb28181pro.api.PlaybackSpeed:output_type -> gb28181pro.BaseResponse
	72, // 153: gb28181pro.api.GetGroups:output_type -> gb28181pro.GroupsListResponse
	0,  // 154: gb28181pro.api.AddGroup:output_type -> gb28181pro.BaseResponse
	0,  // 155: gb28181pro.api.UpdateGroup:output_type -> gb28181pro.BaseResponse
	0,  // 156: gb28181pro.api.DeleteGroup:output_type -> gb28181pro.BaseResponse
	0,  // 157: gb28181pro.api.AddGroupChannel:output_type -> gb28181pro.BaseResponse
	0,  // 158: gb28181pro.api.DeleteGroupChannel:output_type -> gb28181pro.BaseResponse
	79, // 159: gb28181pro.api.GetGroupChannels:output_type -> gb28181pro.GroupChannelsResponse
	0,  // 160: gb28181pro.api.RemoveDevice:output_type -> gb28181pro.BaseResponse
	86, // 161: gb28181pro.api.OpenRTPServer:output_type -> gb28181pro.OpenRTPServerResponse
	96, // [96:162] is the sub-list for method output_type
	30, // [30:96] is the sub-list for method input_type
	30, // [30:30] is the sub-list for extension type_name
	30, // [30:30] is the sub-list for extension extendee
	0,  // [0:30] is the sub-list for field type_name
}

func init() { file_gb28181_proto_init() }
func file_gb28181_proto_init() {
	if File_gb28181_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_gb28181_proto_rawDesc), len(file_gb28181_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   89,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_gb28181_proto_goTypes,
		DependencyIndexes: file_gb28181_proto_depIdxs,
		MessageInfos:      file_gb28181_proto_msgTypes,
	}.Build()
	File_gb28181_proto = out.File
	file_gb28181_proto_goTypes = nil
	file_gb28181_proto_depIdxs = nil
}
