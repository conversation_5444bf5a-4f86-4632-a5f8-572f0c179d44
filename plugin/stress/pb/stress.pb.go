// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: stress.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CountResponseData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PushCount     uint32                 `protobuf:"varint,1,opt,name=pushCount,proto3" json:"pushCount,omitempty"`
	PullCount     uint32                 `protobuf:"varint,2,opt,name=pullCount,proto3" json:"pullCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountResponseData) Reset() {
	*x = CountResponseData{}
	mi := &file_stress_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResponseData) ProtoMessage() {}

func (x *CountResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_stress_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResponseData.ProtoReflect.Descriptor instead.
func (*CountResponseData) Descriptor() ([]byte, []int) {
	return file_stress_proto_rawDescGZIP(), []int{0}
}

func (x *CountResponseData) GetPushCount() uint32 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

func (x *CountResponseData) GetPullCount() uint32 {
	if x != nil {
		return x.PullCount
	}
	return 0
}

type CountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *CountResponseData     `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountResponse) Reset() {
	*x = CountResponse{}
	mi := &file_stress_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountResponse) ProtoMessage() {}

func (x *CountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_stress_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountResponse.ProtoReflect.Descriptor instead.
func (*CountResponse) Descriptor() ([]byte, []int) {
	return file_stress_proto_rawDescGZIP(), []int{1}
}

func (x *CountResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CountResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CountResponse) GetData() *CountResponseData {
	if x != nil {
		return x.Data
	}
	return nil
}

type PushRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	RemoteURL     string                 `protobuf:"bytes,3,opt,name=remoteURL,proto3" json:"remoteURL,omitempty"`
	PushCount     int32                  `protobuf:"varint,4,opt,name=pushCount,proto3" json:"pushCount,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushRequest) Reset() {
	*x = PushRequest{}
	mi := &file_stress_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushRequest) ProtoMessage() {}

func (x *PushRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stress_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushRequest.ProtoReflect.Descriptor instead.
func (*PushRequest) Descriptor() ([]byte, []int) {
	return file_stress_proto_rawDescGZIP(), []int{2}
}

func (x *PushRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PushRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *PushRequest) GetRemoteURL() string {
	if x != nil {
		return x.RemoteURL
	}
	return ""
}

func (x *PushRequest) GetPushCount() int32 {
	if x != nil {
		return x.PushCount
	}
	return 0
}

type PullRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RemoteURL     string                 `protobuf:"bytes,1,opt,name=remoteURL,proto3" json:"remoteURL,omitempty"`
	Protocol      string                 `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	PullCount     int32                  `protobuf:"varint,3,opt,name=pullCount,proto3" json:"pullCount,omitempty"`
	TestMode      int32                  `protobuf:"varint,4,opt,name=testMode,proto3" json:"testMode,omitempty"` // 0: pull, 1: pull without publish
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PullRequest) Reset() {
	*x = PullRequest{}
	mi := &file_stress_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PullRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PullRequest) ProtoMessage() {}

func (x *PullRequest) ProtoReflect() protoreflect.Message {
	mi := &file_stress_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PullRequest.ProtoReflect.Descriptor instead.
func (*PullRequest) Descriptor() ([]byte, []int) {
	return file_stress_proto_rawDescGZIP(), []int{3}
}

func (x *PullRequest) GetRemoteURL() string {
	if x != nil {
		return x.RemoteURL
	}
	return ""
}

func (x *PullRequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *PullRequest) GetPullCount() int32 {
	if x != nil {
		return x.PullCount
	}
	return 0
}

func (x *PullRequest) GetTestMode() int32 {
	if x != nil {
		return x.TestMode
	}
	return 0
}

var File_stress_proto protoreflect.FileDescriptor

const file_stress_proto_rawDesc = "" +
	"\n" +
	"\fstress.proto\x12\x06stress\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\fglobal.proto\"O\n" +
	"\x11CountResponseData\x12\x1c\n" +
	"\tpushCount\x18\x01 \x01(\rR\tpushCount\x12\x1c\n" +
	"\tpullCount\x18\x02 \x01(\rR\tpullCount\"l\n" +
	"\rCountResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12-\n" +
	"\x04data\x18\x03 \x01(\v2\x19.stress.CountResponseDataR\x04data\"\x85\x01\n" +
	"\vPushRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\x12\x1c\n" +
	"\tremoteURL\x18\x03 \x01(\tR\tremoteURL\x12\x1c\n" +
	"\tpushCount\x18\x04 \x01(\x05R\tpushCount\"\x81\x01\n" +
	"\vPullRequest\x12\x1c\n" +
	"\tremoteURL\x18\x01 \x01(\tR\tremoteURL\x12\x1a\n" +
	"\bprotocol\x18\x02 \x01(\tR\bprotocol\x12\x1c\n" +
	"\tpullCount\x18\x03 \x01(\x05R\tpullCount\x12\x1a\n" +
	"\btestMode\x18\x04 \x01(\x05R\btestMode2\xf1\x03\n" +
	"\x03api\x12m\n" +
	"\tStartPush\x12\x13.stress.PushRequest\x1a\x17.global.SuccessResponse\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/stress/api/push/{protocol}/{pushCount}\x12m\n" +
	"\tStartPull\x12\x13.stress.PullRequest\x1a\x17.global.SuccessResponse\"2\x82\xd3\xe4\x93\x02,:\x01*\"'/stress/api/pull/{protocol}/{pullCount}\x12T\n" +
	"\bGetCount\x12\x16.google.protobuf.Empty\x1a\x15.stress.CountResponse\"\x19\x82\xd3\xe4\x93\x02\x13\x12\x11/stress/api/count\x12Z\n" +
	"\bStopPush\x12\x16.google.protobuf.Empty\x1a\x17.global.SuccessResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\"\x15/stress/api/stop/push\x12Z\n" +
	"\bStopPull\x12\x16.google.protobuf.Empty\x1a\x17.global.SuccessResponse\"\x1d\x82\xd3\xe4\x93\x02\x17\"\x15/stress/api/stop/pullB\x1eZ\x1cm7s.live/v5/plugin/stress/pbb\x06proto3"

var (
	file_stress_proto_rawDescOnce sync.Once
	file_stress_proto_rawDescData []byte
)

func file_stress_proto_rawDescGZIP() []byte {
	file_stress_proto_rawDescOnce.Do(func() {
		file_stress_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_stress_proto_rawDesc), len(file_stress_proto_rawDesc)))
	})
	return file_stress_proto_rawDescData
}

var file_stress_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_stress_proto_goTypes = []any{
	(*CountResponseData)(nil),  // 0: stress.CountResponseData
	(*CountResponse)(nil),      // 1: stress.CountResponse
	(*PushRequest)(nil),        // 2: stress.PushRequest
	(*PullRequest)(nil),        // 3: stress.PullRequest
	(*emptypb.Empty)(nil),      // 4: google.protobuf.Empty
	(*pb.SuccessResponse)(nil), // 5: global.SuccessResponse
}
var file_stress_proto_depIdxs = []int32{
	0, // 0: stress.CountResponse.data:type_name -> stress.CountResponseData
	2, // 1: stress.api.StartPush:input_type -> stress.PushRequest
	3, // 2: stress.api.StartPull:input_type -> stress.PullRequest
	4, // 3: stress.api.GetCount:input_type -> google.protobuf.Empty
	4, // 4: stress.api.StopPush:input_type -> google.protobuf.Empty
	4, // 5: stress.api.StopPull:input_type -> google.protobuf.Empty
	5, // 6: stress.api.StartPush:output_type -> global.SuccessResponse
	5, // 7: stress.api.StartPull:output_type -> global.SuccessResponse
	1, // 8: stress.api.GetCount:output_type -> stress.CountResponse
	5, // 9: stress.api.StopPush:output_type -> global.SuccessResponse
	5, // 10: stress.api.StopPull:output_type -> global.SuccessResponse
	6, // [6:11] is the sub-list for method output_type
	1, // [1:6] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_stress_proto_init() }
func file_stress_proto_init() {
	if File_stress_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_stress_proto_rawDesc), len(file_stress_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_stress_proto_goTypes,
		DependencyIndexes: file_stress_proto_depIdxs,
		MessageInfos:      file_stress_proto_msgTypes,
	}.Build()
	File_stress_proto = out.File
	file_stress_proto_goTypes = nil
	file_stress_proto_depIdxs = nil
}
