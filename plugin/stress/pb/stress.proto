syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "global.proto";
package stress;
option go_package="m7s.live/v5/plugin/stress/pb";

service api {
  rpc StartPush (PushRequest) returns (global.SuccessResponse) {
    option (google.api.http) = {
      post: "/stress/api/push/{protocol}/{pushCount}"
      body: "*"
    };
  }
  rpc StartPull (PullRequest) returns (global.SuccessResponse) {
    option (google.api.http) = {
      post: "/stress/api/pull/{protocol}/{pullCount}"
      body: "*"
    };
  }
  rpc GetCount (google.protobuf.Empty) returns (CountResponse) {
    option (google.api.http) = {
      get: "/stress/api/count"
    };
  }
  rpc StopPush (google.protobuf.Empty) returns (global.SuccessResponse) {
    option (google.api.http) = {
      post: "/stress/api/stop/push"
    };
  }
  rpc StopPull (google.protobuf.Empty) returns (global.SuccessResponse) {
    option (google.api.http) = {
      post: "/stress/api/stop/pull"
    };
  }
}

message CountResponseData  {
  uint32 pushCount = 1;
  uint32 pullCount = 2;
}

message CountResponse {
  uint32 code = 1;
  string message = 2;
  CountResponseData data = 3;
}

message PushRequest {
  string streamPath = 1;
  string protocol = 2;
  string remoteURL = 3;
  int32  pushCount = 4;
}

message PullRequest {
  string remoteURL = 1;
  string protocol = 2;
  int32  pullCount = 3;
  int32 testMode = 4; // 0: pull, 1: pull without publish
}