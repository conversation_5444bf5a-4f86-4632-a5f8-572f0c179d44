// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: flv.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqRecordList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Range         string                 `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	PageNum       uint32                 `protobuf:"varint,5,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Mode          string                 `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordList) Reset() {
	*x = ReqRecordList{}
	mi := &file_flv_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordList) ProtoMessage() {}

func (x *ReqRecordList) ProtoReflect() protoreflect.Message {
	mi := &file_flv_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordList.ProtoReflect.Descriptor instead.
func (*ReqRecordList) Descriptor() ([]byte, []int) {
	return file_flv_proto_rawDescGZIP(), []int{0}
}

func (x *ReqRecordList) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordList) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *ReqRecordList) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *ReqRecordList) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *ReqRecordList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReqRecordList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReqRecordList) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

type ReqRecordDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Ids           []uint32               `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	StartTime     string                 `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       string                 `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Range         string                 `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordDelete) Reset() {
	*x = ReqRecordDelete{}
	mi := &file_flv_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordDelete) ProtoMessage() {}

func (x *ReqRecordDelete) ProtoReflect() protoreflect.Message {
	mi := &file_flv_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordDelete.ProtoReflect.Descriptor instead.
func (*ReqRecordDelete) Descriptor() ([]byte, []int) {
	return file_flv_proto_rawDescGZIP(), []int{1}
}

func (x *ReqRecordDelete) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordDelete) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReqRecordDelete) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ReqRecordDelete) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ReqRecordDelete) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

var File_flv_proto protoreflect.FileDescriptor

const file_flv_proto_rawDesc = "" +
	"\n" +
	"\tflv.proto\x12\x03flv\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\fglobal.proto\"\xb7\x01\n" +
	"\rReqRecordList\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05range\x18\x02 \x01(\tR\x05range\x12\x14\n" +
	"\x05start\x18\x03 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\tR\x03end\x12\x18\n" +
	"\apageNum\x18\x05 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x06 \x01(\rR\bpageSize\x12\x12\n" +
	"\x04mode\x18\a \x01(\tR\x04mode\"\x91\x01\n" +
	"\x0fReqRecordDelete\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x10\n" +
	"\x03ids\x18\x02 \x03(\rR\x03ids\x12\x1c\n" +
	"\tstartTime\x18\x03 \x01(\tR\tstartTime\x12\x18\n" +
	"\aendTime\x18\x04 \x01(\tR\aendTime\x12\x14\n" +
	"\x05range\x18\x05 \x01(\tR\x05range2\x9e\x02\n" +
	"\x03api\x12]\n" +
	"\x04List\x12\x12.flv.ReqRecordList\x1a\x1a.global.RecordResponseList\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/flv/api/list/{streamPath=**}\x12T\n" +
	"\aCatalog\x12\x16.google.protobuf.Empty\x1a\x17.global.ResponseCatalog\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/flv/api/catalog\x12b\n" +
	"\x06Delete\x12\x14.flv.ReqRecordDelete\x1a\x16.global.ResponseDelete\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/flv/api/delete/{streamPath=**}B\x1bZ\x19m7s.live/v5/plugin/flv/pbb\x06proto3"

var (
	file_flv_proto_rawDescOnce sync.Once
	file_flv_proto_rawDescData []byte
)

func file_flv_proto_rawDescGZIP() []byte {
	file_flv_proto_rawDescOnce.Do(func() {
		file_flv_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_flv_proto_rawDesc), len(file_flv_proto_rawDesc)))
	})
	return file_flv_proto_rawDescData
}

var file_flv_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_flv_proto_goTypes = []any{
	(*ReqRecordList)(nil),         // 0: flv.ReqRecordList
	(*ReqRecordDelete)(nil),       // 1: flv.ReqRecordDelete
	(*emptypb.Empty)(nil),         // 2: google.protobuf.Empty
	(*pb.RecordResponseList)(nil), // 3: global.RecordResponseList
	(*pb.ResponseCatalog)(nil),    // 4: global.ResponseCatalog
	(*pb.ResponseDelete)(nil),     // 5: global.ResponseDelete
}
var file_flv_proto_depIdxs = []int32{
	0, // 0: flv.api.List:input_type -> flv.ReqRecordList
	2, // 1: flv.api.Catalog:input_type -> google.protobuf.Empty
	1, // 2: flv.api.Delete:input_type -> flv.ReqRecordDelete
	3, // 3: flv.api.List:output_type -> global.RecordResponseList
	4, // 4: flv.api.Catalog:output_type -> global.ResponseCatalog
	5, // 5: flv.api.Delete:output_type -> global.ResponseDelete
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_flv_proto_init() }
func file_flv_proto_init() {
	if File_flv_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_flv_proto_rawDesc), len(file_flv_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_flv_proto_goTypes,
		DependencyIndexes: file_flv_proto_depIdxs,
		MessageInfos:      file_flv_proto_msgTypes,
	}.Build()
	File_flv_proto = out.File
	file_flv_proto_goTypes = nil
	file_flv_proto_depIdxs = nil
}
