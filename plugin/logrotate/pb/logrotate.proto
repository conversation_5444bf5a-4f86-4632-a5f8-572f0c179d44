syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
package logrotate;
option go_package="m7s.live/v5/plugin/logrotate/pb";

service api {
  rpc List (google.protobuf.Empty) returns (ResponseFileInfo) {
    option (google.api.http) = {
      get: "/logrotate/api/list"
    };
  }
  rpc Get (RequestFileInfo) returns (ResponseOpen) {
    option (google.api.http) = {
      get: "/logrotate/api/get/{fileName=**}"
    };
  }
}

message ResponseOpen {
  int32 code = 1;
  string message = 2;
  string data = 3;
}

message RequestFileInfo {
  string fileName = 1;
}

message ResponseFileInfo {
  int32 code = 1;
  string message = 2;
  repeated FileInfo data = 3;
}

message FileInfo {
  string name = 1;
  int64 size = 2;
}