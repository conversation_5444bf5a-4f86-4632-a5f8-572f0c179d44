File: testbinary
Type: cpu
Duration: 10s, Total samples = 1.12s (11.20%)
-----------+-------------------------------------------------------
      key1:  tag1
      key2:  tag1
        1s   0000000000001000 line1000 testdata/file1000.src:1
             0000000000002000 line2001 testdata/file2000.src:9 (inline)
             0000000000002000 line2000 testdata/file2000.src:4
             0000000000003000 line3002 testdata/file3000.src:2 (inline)
             0000000000003000 line3001 testdata/file3000.src:5 (inline)
             0000000000003000 line3000 testdata/file3000.src:6
-----------+-------------------------------------------------------
      key1:  tag2
      key3:  tag2
     100ms   0000000000001000 line1000 testdata/file1000.src:1
             0000000000003001 line3001 testdata/file3000.src:8 (inline)
             0000000000003001 line3000 testdata/file3000.src:9
-----------+-------------------------------------------------------
      key1:  tag3
      key2:  tag2
      10ms   0000000000002000 line2001 testdata/file2000.src:9 (inline)
             0000000000002000 line2000 testdata/file2000.src:4
             0000000000003002 line3002 testdata/file3000.src:5 (inline)
             0000000000003002 line3000 testdata/file3000.src:9
-----------+-------------------------------------------------------
      key1:  tag4
      key2:  tag1
      10ms   0000000000003000 line3002 testdata/file3000.src:2 (inline)
             0000000000003000 line3001 testdata/file3000.src:5 (inline)
             0000000000003000 line3000 testdata/file3000.src:6
-----------+-------------------------------------------------------
