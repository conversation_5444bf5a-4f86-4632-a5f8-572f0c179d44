digraph "unnamed" {
node [style=filled fillcolor="#f8f8f8"]
subgraph cluster_L { "Build ID: buildid-contention" [shape=box fontsize=16 label="Build ID: buildid-contention\lComment #1\lComment #2\lType: delay\lActive filters:\l   focus=[X1]000\l   ignore=[X3]002\lShowing nodes accounting for 40.96ms, 27.40% of 149.50ms total\l\lSee https://git.io/JfYMW for how to read the graph\l"] }
N1 [label="****************\nline1000\nfile1000.src:1\n40.96ms (27.40%)" id="node1" fontsize=24 shape=box tooltip="**************** line1000 testdata/file1000.src:1 (40.96ms)" color="#b23900" fillcolor="#edddd5"]
N2 [label="****************\nline3000\nfile3000.src:5\n0 of 40.96ms (27.40%)" id="node2" fontsize=8 shape=box tooltip="**************** line3000 testdata/file3000.src:5 (40.96ms)" color="#b23900" fillcolor="#edddd5"]
N3 [label="****************\nline3001\nfile3000.src:3\n0 of 40.96ms (27.40%)" id="node3" fontsize=8 shape=box tooltip="**************** line3001 testdata/file3000.src:3 (40.96ms)" color="#b23900" fillcolor="#edddd5"]
N2 -> N3 [label=" 40.96ms\n (inline)" weight=28 penwidth=2 color="#b23900" tooltip="**************** line3000 testdata/file3000.src:5 -> **************** line3001 testdata/file3000.src:3 (40.96ms)" labeltooltip="**************** line3000 testdata/file3000.src:5 -> **************** line3001 testdata/file3000.src:3 (40.96ms)"]
N3 -> N1 [label=" 40.96ms" weight=28 penwidth=2 color="#b23900" tooltip="**************** line3001 testdata/file3000.src:3 -> **************** line1000 testdata/file1000.src:1 (40.96ms)" labeltooltip="**************** line3001 testdata/file3000.src:3 -> **************** line1000 testdata/file1000.src:1 (40.96ms)"]
}
