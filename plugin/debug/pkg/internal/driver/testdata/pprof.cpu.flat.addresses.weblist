<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>testbinary cpu</title>
  
  <style type="text/css">
body #content{
font-family: sans-serif;
}
h1 {
  font-size: 1.5em;
}
.legend {
  font-size: 1.25em;
}
.line, .nop, .unimportant {
  color: #aaaaaa;
}
.inlinesrc {
  color: #000066;
}
.livesrc {
cursor: pointer;
}
.livesrc:hover {
background-color: #eeeeee;
}
.asm {
color: #008800;
display: none;
}
</style>
  <script type="text/javascript">
function pprof_toggle_asm(e) {
  var target;
  if (!e) e = window.event;
  if (e.target) target = e.target;
  else if (e.srcElement) target = e.srcElement;

  if (target) {
    var asm = target.nextSibling;
    if (asm && asm.className == "asm") {
      asm.style.display = (asm.style.display == "block" ? "" : "block");
      e.preventDefault();
      return false;
    }
  }
}
</script>
</head>
<body>

<div class="legend">File: testbinary<br>
Type: cpu<br>
Duration: 10s, Total samples = 1.12s (11.20%)<br>Total: 1.12s</div><h2>line1000</h2><p class="filename">testdata/file1000.src</p>
<pre onClick="pprof_toggle_asm(event)">
  Total:       1.10s      1.10s (flat, cum) 98.21%
<span class=line>      1</span> <span class=livesrc>       1.10s      1.10s           line1 </span><span class=asm>               1.10s      1.10s     1000:     instruction one                                                              <span class=unimportant>file1000.src:1</span>
                   .          .     1001:     instruction two                                                              <span class=unimportant>file1000.src:1</span>
                                     ⋮
                   .          .     1003:     instruction four                                                             <span class=unimportant>file1000.src:1</span>
</span>
<span class=line>      2</span> <span class=livesrc>           .          .           line2 </span><span class=asm>                   .          .     1002:     instruction three                                                            <span class=unimportant>file1000.src:2</span>
</span>
<span class=line>      3</span> <span class=nop>           .          .           line3 </span>
<span class=line>      4</span> <span class=nop>           .          .           line4 </span>
<span class=line>      5</span> <span class=nop>           .          .           line5 </span>
<span class=line>      6</span> <span class=nop>           .          .           line6 </span>
<span class=line>      7</span> <span class=nop>           .          .           line7 </span>
</pre>
<h2>line3000</h2><p class="filename">testdata/file3000.src</p>
<pre onClick="pprof_toggle_asm(event)">
  Total:        10ms      1.12s (flat, cum)   100%
<span class=line>      1</span> <span class=nop>           .          .           line1 </span>
<span class=line>      2</span> <span class=nop>           .          .           line2 </span>
<span class=line>      3</span> <span class=nop>           .          .           line3 </span>
<span class=line>      4</span> <span class=nop>           .          .           line4 </span>
<span class=line>      5</span> <span class=nop>           .          .           line5 </span>
<span class=line>      6</span> <span class=livesrc>        10ms      1.01s           line6 </span><span class=asm>                                          <span class=inlinesrc>    line5                                                                       </span> <span class=unimportant>file3000.src:5</span>
                                          <span class=inlinesrc>        line2                                                                   </span> <span class=unimportant>file3000.src:2</span>
                10ms      1.01s     3000:             instruction one                                                      <span class=unimportant>file3000.src:2</span>
</span>
<span class=line>      7</span> <span class=nop>           .          .           line7 </span>
<span class=line>      8</span> <span class=nop>           .          .           line8 </span>
<span class=line>      9</span> <span class=livesrc>           .      110ms           line9 </span><span class=asm>                                          <span class=inlinesrc>    line8                                                                       </span> <span class=unimportant>file3000.src:8</span>
                   .      100ms     3001:         instruction two                                                          <span class=unimportant>file3000.src:8</span>
                                          <span class=inlinesrc>    line5                                                                       </span> <span class=unimportant>file3000.src:5</span>
                   .       10ms     3002:         instruction three                                                        <span class=unimportant>file3000.src:5</span>
                   .          .     3003:         instruction four                                                         <span class=unimportant></span>
                   .          .     3004:         instruction five                                                         <span class=unimportant></span>
</span>
<span class=line>     10</span> <span class=nop>           .          .           line0 </span>
<span class=line>     11</span> <span class=nop>           .          .           line1 </span>
<span class=line>     12</span> <span class=nop>           .          .           line2 </span>
<span class=line>     13</span> <span class=nop>           .          .           line3 </span>
<span class=line>     14</span> <span class=nop>           .          .           line4 </span>
</pre>

</body>
</html>
