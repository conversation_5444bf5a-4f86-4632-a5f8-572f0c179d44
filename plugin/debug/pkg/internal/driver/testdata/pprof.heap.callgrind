positions: instr line
events: inuse_space(MB)

ob=
fl=(1) testdata/file2000.src
fn=(1) line2001
0x2000 2 62
cfl=(2) testdata/file1000.src
cfn=(2) line1000
calls=0 0x1000 1
* * 0

ob=
fl=(3) testdata/file3000.src
fn=(3) line3002
+4096 3 31
cfl=(1)
cfn=(4) line2000
calls=0 * 3
* * 0

ob=
fl=(2)
fn=(2)
-8192 1 4

ob=
fl=(1)
fn=(4)
+4096 3 0
cfl=(1)
cfn=(1)
calls=0 +4096 2
* * 63

ob=
fl=(3)
fn=(5) line3000
+4096 4 0
cfl=(3)
cfn=(6) line3001
calls=0 +4096 2
* * 32

ob=
fl=(3)
fn=(6)
* 2 0
cfl=(3)
cfn=(3)
calls=0 * 3
* * 32

ob=
fl=(3)
fn=(5)
+1 4 0
cfl=(3)
cfn=(6)
calls=0 +1 2
* * 3

ob=
fl=(3)
fn=(6)
* 2 0
cfl=(2)
cfn=(2)
calls=0 -8193 1
* * 3

ob=
fl=(3)
fn=(5)
+1 4 0
cfl=(3)
cfn=(3)
calls=0 +1 3
* * 62

ob=
fl=(3)
fn=(3)
* 3 0
cfl=(1)
cfn=(4)
calls=0 -4098 3
* * 62
