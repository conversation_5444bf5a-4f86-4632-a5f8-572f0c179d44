PeriodType: thread count
Period: 1
Samples:
thread/count
          1: 1 2 3 4 5 6 7 
          1: 1 8 9 10 11 12 13 14 15 16 17 7 
          1: 18 19 20 21 22 10 11 12 23 24 25 26 27 7 
          1: 1 28 22 10 11 12 23 24 25 26 27 7 
Locations
     1: 0xbc8f1c M=1 
     2: 0xbcae54 M=1 
     3: 0xbcb5f4 M=1 
     4: 0x40b687 M=1 
     5: 0x4d5f50 M=1 
     6: 0x40be30 M=1 
     7: 0x7eff052e111c M=4 
     8: 0xbcbcff M=1 
     9: 0xa47f5f M=1 
    10: 0xa456e3 M=1 
    11: 0x7eff05b6d14d M=7 
    12: 0xa45b95 M=1 
    13: 0xa48927 M=1 
    14: 0xbaa17e M=1 
    15: 0xbaa9f8 M=1 
    16: 0xbb0d20 M=1 
    17: 0x40bce3 M=1 
    18: 0x7eff052dfa93 M=4 
    19: 0x7a1955 M=1 
    20: 0x7a1c44 M=1 
    21: 0x7a2726 M=1 
    22: 0x7a296c M=1 
    23: 0x7a37d1 M=1 
    24: 0x7a3e8c M=1 
    25: 0xbbff76 M=1 
    26: 0x79ec1b M=1 
    27: 0x40bd6d M=1 
    28: 0x7a2690 M=1 
Mappings
1: 0x400000/0xfcb000/0x0 /home/<USER>/cppbench/cppbench_server_main.unstripped  
2: 0x7eff04e98000/0x7eff04e9b000/0x0 /lib/libnss_cache-2.15.so  
3: 0x7eff0509c000/0x7eff050a8000/0x0 /lib/libnss_files-2.15.so  
4: 0x7eff052a9000/0x7eff05457000/0x0 /lib/libc-2.15.so  
5: 0x7eff05662000/0x7eff0575d000/0x0 /lib/libm-2.15.so  
6: 0x7eff0595e000/0x7eff05965000/0x0 /lib/librt-2.15.so  
7: 0x7eff05b66000/0x7eff05b7e000/0x0 /lib/libpthread-2.15.so  
8: 0x7eff05d83000/0x7eff05d86000/0x0 /lib/libdl-2.15.so  
9: 0x7eff05f87000/0x7eff05f90000/0x0 /lib/libcrypt-2.15.so  
10: 0x7eff061c0000/0x7eff061e4000/0x0 /lib/ld-2.15.so  
11: 0x7fff2edff000/0x7fff2ee00000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
