PeriodType: contentions count
Period: 100
Duration: 1h40
Samples:
contentions/count delay/microseconds
        100        100: 1 2 
        100       1400: 3 4 5 6 7 8 9 10 11 12 13 14 15 16 10 17 18 19 20 21 22 23 24 25 26 27 28 29 
        200        200: 1 2 
        300        200: 30 31 32 
Locations
     1: 0x0 com.example.function03 source.java:3:0 s=0
     2: 0x0 com.example.function04 source.java:4:0 s=0
     3: 0x0 com.example.function0d source.java:0:0 s=0
     4: 0x0 com.example.function0e source.java:0:0 s=0
     5: 0x0 com.example.function0f source.java:0:0 s=0
     6: 0x0 com.example.function10 source.java:10:0 s=0
     7: 0x0 com.example.function11 source.java:11:0 s=0
     8: 0x0 com.example.function12 source.java:12:0 s=0
     9: 0x0 com.example.function13 source.java:13:0 s=0
    10: 0x0 com.example.function14 source.java:14:0 s=0
    11: 0x0 com.example.function17 source.java:17:0 s=0
    12: 0x0 com.example.function18 source.java:18:0 s=0
    13: 0x0 com.example.function19 source.java:19:0 s=0
    14: 0x0 com.example.function1a source.java:1:0 s=0
    15: 0x0 com.example.function1b source.java:1:0 s=0
    16: 0x0 com.example.function1c source.java:1:0 s=0
    17: 0x0 com.example.function29 source.java:29:0 s=0
    18: 0x0 com.example.function2a source.java:2:0 s=0
    19: 0x0 com.example.function2b source.java:2:0 s=0
    20: 0x0 com.example.function2c source.java:2:0 s=0
    21: 0x0 com.example.function2d source.java:2:0 s=0
    22: 0x0 com.example.function2e source.java:2:0 s=0
    23: 0x0 com.example.function2f source.java:2:0 s=0
    24: 0x0 com.example.function30 source.java:30:0 s=0
    25: 0x0 com.example.function31 source.java:31:0 s=0
    26: 0x0 com.example.function32 source.java:32:0 s=0
    27: 0x0 com.example.function33 source.java:33:0 s=0
    28: 0x0 com.example.function34 source.java:34:0 s=0
    29: 0x0 com.example.function35 source.java:35:0 s=0
    30: 0x0 com.example.function36 source.java:36:0 s=0
    31: 0x0 com.example.function37 source.java:37:0 s=0
    32: 0x0 com.example.function38 source.java:38:0 s=0
Mappings
