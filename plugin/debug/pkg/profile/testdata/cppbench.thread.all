--- threadz 1 ---

--- Thread 7eff063d9940 (name: main/25376) stack: ---
  PC:  0x00bc8f1c: helper(arg*)
  0x0040be31: main
  0x7eff052e111d: __libc_start_main
--- Thread 7eff04e97700 (name: thread1/25379) stack: ---
  PC:  0x7eff05b74f7d: nanosleep
  0x7eff05b6d14e: start_thread
      creator:
  0x0040bce4: main
  0x7eff052e111d: __libc_start_main
--- Thread 7eff04770700 (name: thread2/25382) stack: ---
  PC:  0x00bc8f1c: Wait(int)
  0x7eff05b6d14e: start_thread
      creator:
  0x0040bd6e: main
  0x7eff052e111d: __libc_start_main
--- Thread 7eff0464d700 (name: thread3/25383) stack: ---
  [same as previous thread]
--- Memory map: ---
  00400000-00fcb000: /home/<USER>/cppbench/cppbench_server_main
  7eff04e98000-7eff04e9b000: /lib/libnss_cache-2.15.so
  7eff0509c000-7eff050a8000: /lib/libnss_files-2.15.so
  7eff052a9000-7eff05457000: /lib/libc-2.15.so
  7eff05662000-7eff0575d000: /lib/libm-2.15.so
  7eff0595e000-7eff05965000: /lib/librt-2.15.so
  7eff05b66000-7eff05b7e000: /lib/libpthread-2.15.so
  7eff05d83000-7eff05d86000: /lib/libdl-2.15.so
  7eff05f87000-7eff05f90000: /lib/libcrypt-2.15.so
  7eff061c0000-7eff061e4000: /lib/ld-2.15.so
  7fff2edff000-7fff2ee00000: [vdso]
  ffffffffff600000-ffffffffff601000: [vsyscall]
