PeriodType: cpu nanoseconds
Period: 10000000
Samples:
samples/count cpu/nanoseconds
          1   10000000: 1 2 3 4 5 6 7 8 9 10 
          1   10000000: 11 2 3 4 5 6 7 8 9 10 
          1   10000000: 1 2 3 4 5 6 7 8 9 10 
          1   10000000: 12 13 14 15 16 17 18 3 4 5 6 7 8 9 10 
        542 5420000000: 19 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 20 17 18 3 4 5 6 7 8 9 10 
         10  100000000: 21 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 22 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 23 24 25 2 3 4 5 6 7 8 9 10 
          3   30000000: 26 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 27 16 17 18 3 4 5 6 7 8 9 10 
          2   20000000: 28 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 29 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 30 31 32 33 34 35 36 37 38 9 10 
          3   30000000: 39 40 41 24 25 2 3 4 5 6 7 8 9 10 
          2   20000000: 42 40 41 24 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 43 40 41 24 25 2 3 4 5 6 7 8 9 10 
          2   20000000: 44 45 41 24 25 2 3 4 5 6 7 8 9 10 
         67  670000000: 46 2 3 4 5 6 7 8 9 10 
         20  200000000: 47 2 3 4 5 6 7 8 9 10 
         12  120000000: 48 2 3 4 5 6 7 8 9 10 
          5   50000000: 11 2 3 4 5 6 7 8 9 10 
          1   10000000: 49 10 
          1   10000000: 50 51 52 13 14 15 16 17 18 3 4 5 6 7 8 9 10 
          2   20000000: 53 51 52 13 14 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 54 14 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 55 56 57 58 4 5 6 7 8 9 10 
          1   10000000: 59 41 24 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 60 41 24 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 61 62 63 64 40 41 24 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 65 66 67 68 69 70 71 72 73 74 75 37 38 9 10 
          1   10000000: 76 13 77 15 16 17 18 3 4 5 6 7 8 9 10 
          2   20000000: 78 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 79 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 80 13 77 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 81 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 82 13 14 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 83 13 77 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 83 13 14 15 16 17 18 3 4 5 6 7 8 9 10 
          1   10000000: 30 84 85 86 9 10 
          1   10000000: 87 88 40 41 24 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 89 90 91 92 8 9 10 
          1   10000000: 30 93 8 9 10 
          1   10000000: 30 84 94 9 10 
          1   10000000: 95 3 4 5 6 7 8 9 10 
          1   10000000: 96 97 3 4 5 6 7 8 9 10 
          1   10000000: 98 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 99 25 2 3 4 5 6 7 8 9 10 
          1   10000000: 100 101 102 41 24 25 2 3 4 5 6 7 8 9 10 
          2   20000000: 103 104 91 92 8 9 10 
          1   10000000: 105 104 91 92 8 9 10 
          1   10000000: 106 107 108 109 97 3 4 5 6 7 8 9 10 
Locations
     1: 0x42ef04 M=1 
     2: 0x42e14b M=1 
     3: 0x5261ae M=1 
     4: 0x526ede M=1 
     5: 0x5280aa M=1 
     6: 0x79e809 M=1 
     7: 0x7a251a M=1 
     8: 0x7a296c M=1 
     9: 0xa456e3 M=1 
    10: 0x7f5e541460fd M=7 
    11: 0x42ef17 M=1 
    12: 0xb867c0 M=1 
    13: 0xb82bca M=1 
    14: 0xb87eaa M=1 
    15: 0xb8814b M=1 
    16: 0x42ed5e M=1 
    17: 0x42edc0 M=1 
    18: 0x42e159 M=1 
    19: 0x42ed43 M=1 
    20: 0xc60ea0 M=1 
    21: 0x42ed40 M=1 
    22: 0xbf42fe M=1 
    23: 0xb87d6f M=1 
    24: 0xc635ef M=1 
    25: 0x42ecc2 M=1 
    26: 0xc60f0f M=1 
    27: 0xc610d7 M=1 
    28: 0xc61108 M=1 
    29: 0xb8816e M=1 
    30: 0xbc8f1c M=1 
    31: 0xbcae54 M=1 
    32: 0xbcb5f4 M=1 
    33: 0x40b687 M=1 
    34: 0x535244 M=1 
    35: 0x536bf4 M=1 
    36: 0x42eb0f M=1 
    37: 0x42de64 M=1 
    38: 0xa41281 M=1 
    39: 0xb82dea M=1 
    40: 0xb83002 M=1 
    41: 0xb87d4f M=1 
    42: 0xb82df1 M=1 
    43: 0xb82dd3 M=1 
    44: 0xb82c23 M=1 
    45: 0xb82fd1 M=1 
    46: 0x42ef13 M=1 
    47: 0x42ef0b M=1 
    48: 0x42ef0f M=1 
    49: 0x7f5e53999f13 M=4 
    50: 0xb8591b M=1 
    51: 0xb85e48 M=1 
    52: 0xb82ae3 M=1 
    53: 0xb85893 M=1 
    54: 0xb88cdc M=1 
    55: 0x698000 M=1 
    56: 0x653f4b M=1 
    57: 0x54dc65 M=1 
    58: 0x525120 M=1 
    59: 0xb88d84 M=1 
    60: 0xb88d98 M=1 
    61: 0xb86591 M=1 
    62: 0xb859de M=1 
    63: 0xb862de M=1 
    64: 0xb82d5e M=1 
    65: 0x967171 M=1 
    66: 0x964990 M=1 
    67: 0x448584 M=1 
    68: 0x5476d7 M=1 
    69: 0x4f1be0 M=1 
    70: 0x4f34db M=1 
    71: 0x4f8a9a M=1 
    72: 0x5388df M=1 
    73: 0x573c5a M=1 
    74: 0x4a4168 M=1 
    75: 0x42eb03 M=1 
    76: 0xb82a31 M=1 
    77: 0xb87f07 M=1 
    78: 0xb87e76 M=1 
    79: 0xb87e7e M=1 
    80: 0xb82a36 M=1 
    81: 0xb87ede M=1 
    82: 0xb82a55 M=1 
    83: 0xb82b08 M=1 
    84: 0xbcbcff M=1 
    85: 0xbcbea4 M=1 
    86: 0xa40112 M=1 
    87: 0xb85e87 M=1 
    88: 0xb82d77 M=1 
    89: 0x79eb32 M=1 
    90: 0x7a18e8 M=1 
    91: 0x7a1c44 M=1 
    92: 0x7a2726 M=1 
    93: 0x7a2690 M=1 
    94: 0x89f186 M=1 
    95: 0xc60eb7 M=1 
    96: 0x521c7f M=1 
    97: 0x5194c8 M=1 
    98: 0xc634f0 M=1 
    99: 0xc63245 M=1 
   100: 0xb867d8 M=1 
   101: 0xb82cf2 M=1 
   102: 0xb82f82 M=1 
   103: 0x7f5e538b9a93 M=4 
   104: 0x7a1955 M=1 
   105: 0x7f5e538b9a97 M=4 
   106: 0x7e0f10 M=1 
   107: 0x7e0b5d M=1 
   108: 0x6ab44f M=1 
   109: 0x521d51 M=1 
Mappings
1: 0x400000/0xfcb000/0x0 cppbench_server_main  
2: 0x7f5e53061000/0x7f5e53062000/0x0 /lib/libnss_borg-2.15.so  
3: 0x7f5e53264000/0x7f5e53270000/0x0 /lib/libnss_files-2.15.so  
4: 0x7f5e53883000/0x7f5e53a31000/0x0 /lib/libc-2.15.so  
5: 0x7f5e53c3b000/0x7f5e53d36000/0x0 /lib/libm-2.15.so  
6: 0x7f5e53f37000/0x7f5e53f3e000/0x0 /lib/librt-2.15.so  
7: 0x7f5e5413f000/0x7f5e54157000/0x0 /lib/libpthread-2.15.so  
8: 0x7f5e5435c000/0x7f5e5435e000/0x0 /lib/libdl-2.15.so  
9: 0x7f5e54560000/0x7f5e54569000/0x0 /lib/libcrypt-2.15.so  
10: 0x7f5e54799000/0x7f5e547bd000/0x0 /lib/ld-2.15.so  
11: 0x7ffffb56b000/0x7ffffb56d000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
