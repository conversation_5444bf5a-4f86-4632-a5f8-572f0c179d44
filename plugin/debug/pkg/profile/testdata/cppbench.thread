--- threadz 1 ---

--- Thread 7f794ab90940 (name: main/14748) stack: ---
  PC:  0x00bc8f1c: helper(arg *)
  0x0040be31: main
  0x7f7949a9811d: __libc_start_main
--- Thread 7f794964e700 (name: thread1/14751) stack: ---
  PC:  0x7f794a32bf7d: nanosleep
  0x7f794a32414e: start_thread
      creator: 0xa45b96 0xa460b4 0xbaa17f 0xbaa9f9 0xbb0d21 0x40bce4 0x7f7949a9811d
--- Thread 7f794934c700 (name: thread2/14752) stack: ---
  PC:  0x00bc8f1c: Wait(int)
  0x7f794a32414e: start_thread
      creator: 0xa45b96 0xa48928 0xbaa17f 0xbaa9f9 0xbb0d21 0x40bce4 0x7f7949a9811d
--- Thread 7f7948978700 (name: thread3/14759) stack: ---
  [same as previous thread]
--- Memory map: ---
  00400000-00fcb000: /home/<USER>/cppbench/cppbench_server_main
  7f794964f000-7f7949652000: /lib/libnss_cache-2.15.so
  7f7949853000-7f794985f000: /lib/libnss_files-2.15.so
  7f7949a60000-7f7949c0e000: /lib/libc-2.15.so
  7f7949e19000-7f7949f14000: /lib/libm-2.15.so
  7f794a115000-7f794a11c000: /lib/librt-2.15.so
  7f794a31d000-7f794a335000: /lib/libpthread-2.15.so
  7f794a53a000-7f794a53d000: /lib/libdl-2.15.so
  7f794a73e000-7f794a747000: /lib/libcrypt-2.15.so
  7f794a977000-7f794a99b000: /lib/ld-2.15.so
  7fffb8dff000-7fffb8e00000: [vdso]
  ffffffffff600000-ffffffffff601000: [vsyscall]
