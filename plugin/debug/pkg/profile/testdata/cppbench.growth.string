PeriodType: space bytes
Period: 1
Samples:
objects/count space/bytes
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 14 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 4 5 6 7 8 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 15 16 17 18 19 20 21 22 23 24 25 26 27 28 29 30 31 32 33 34 35 36 37 38 39 40 41 
                bytes:[2097152]
          1    2097152: 14 42 43 44 45 46 47 48 49 9 10 11 12 13 
                bytes:[2097152]
          1    2097152: 1 2 3 50 51 52 53 54 55 56 57 
                bytes:[2097152]
          1    2097152: 1 2 3 58 59 60 
                bytes:[2097152]
Locations
     1: 0xb83002 M=1 
     2: 0xb87d4f M=1 
     3: 0xc635ef M=1 
     4: 0x42ecc2 M=1 
     5: 0x42e14b M=1 
     6: 0x5261ae M=1 
     7: 0x526ede M=1 
     8: 0x5280aa M=1 
     9: 0x79e809 M=1 
    10: 0x7a251a M=1 
    11: 0x7a296c M=1 
    12: 0xa456e3 M=1 
    13: 0x7fcdc2ff214d M=7 
    14: 0xc635c7 M=1 
    15: 0xafc0ea M=1 
    16: 0xb087b0 M=1 
    17: 0xb0aa7c M=1 
    18: 0xb0b373 M=1 
    19: 0xb12f0f M=1 
    20: 0xb13a91 M=1 
    21: 0xb0c442 M=1 
    22: 0xb145f2 M=1 
    23: 0xb147c9 M=1 
    24: 0xa5dddc M=1 
    25: 0xbbffe5 M=1 
    26: 0xa5e836 M=1 
    27: 0xa65f93 M=1 
    28: 0x5aac9d M=1 
    29: 0x535525 M=1 
    30: 0x535143 M=1 
    31: 0x5aa467 M=1 
    32: 0x7e3ce6 M=1 
    33: 0x7d13a1 M=1 
    34: 0x7e0d27 M=1 
    35: 0x6ab44f M=1 
    36: 0x538d26 M=1 
    37: 0x5390e7 M=1 
    38: 0x5391e2 M=1 
    39: 0x4e9602 M=1 
    40: 0x4faa16 M=1 
    41: 0x4fc5f5 M=1 
    42: 0x8168ff M=1 
    43: 0x8149fc M=1 
    44: 0x813a9f M=1 
    45: 0xbbff76 M=1 
    46: 0x81421b M=1 
    47: 0x4ed413 M=1 
    48: 0x4fd706 M=1 
    49: 0x4de2a1 M=1 
    50: 0xbb5782 M=1 
    51: 0x40acd7 M=1 
    52: 0x61192d M=1 
    53: 0x4b9521 M=1 
    54: 0x4b9f61 M=1 
    55: 0x4ba024 M=1 
    56: 0x40bd85 M=1 
    57: 0x7fcdc276711c M=4 
    58: 0x42d575 M=1 
    59: 0xc25cc5 M=1 
    60: 0x40651a M=1 
Mappings
1: 0x400000/0xfcb000/0x0 cppbench_server_main  
2: 0x7fcdc231e000/0x7fcdc2321000/0x0 /libnss_cache-2.15.so  
3: 0x7fcdc2522000/0x7fcdc252e000/0x0 /libnss_files-2.15.so  
4: 0x7fcdc272f000/0x7fcdc28dd000/0x0 /libc-2.15.so  
5: 0x7fcdc2ae7000/0x7fcdc2be2000/0x0 /libm-2.15.so  
6: 0x7fcdc2de3000/0x7fcdc2dea000/0x0 /librt-2.15.so  
7: 0x7fcdc2feb000/0x7fcdc3003000/0x0 /libpthread-2.15.so  
8: 0x7fcdc3208000/0x7fcdc320a000/0x0 /libdl-2.15.so  
9: 0x7fcdc340c000/0x7fcdc3415000/0x0 /libcrypt-2.15.so  
10: 0x7fcdc3645000/0x7fcdc3669000/0x0 /ld-2.15.so  
11: 0x7fff86bff000/0x7fff86c00000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
