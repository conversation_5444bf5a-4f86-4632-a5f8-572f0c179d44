PeriodType: space bytes
Period: 524288
Samples:
alloc_objects/count alloc_space/bytes inuse_objects/count inuse_space/bytes
          4    2488234          1     829411: 1 2 3 4 5 6 7 8 9 10 
                bytes:[524288]
          1     829411          1     829411: 1 2 3 4 5 6 11 10 
                bytes:[524288]
          2     666237          2     666237: 1 2 3 4 5 12 13 14 15 16 17 10 
                bytes:[262144]
          2     666237          2     666237: 1 2 3 4 5 12 13 14 15 18 19 20 21 10 
                bytes:[262144]
      33192  339890635         51     529424: 1 2 3 4 5 22 23 24 25 26 10 
                bytes:[10240]
        128     526338        128     526338: 1 2 3 4 5 27 28 29 30 10 
                bytes:[4096]
        128     526338        128     526338: 1 2 3 4 5 31 32 33 34 10 
                bytes:[4096]
        256     525312        256     525312: 1 35 36 37 38 10 
                bytes:[2048]
        410     524928        410     524928: 1 39 40 41 42 43 44 45 10 
                bytes:[1280]
       1365     524480       1365     524480: 1 2 3 4 46 47 48 49 50 51 52 53 54 55 56 57 58 59 56 60 61 56 62 63 64 65 66 67 68 69 70 65 
                bytes:[384]
       1820     524432       1820     524432: 1 35 71 72 73 74 75 42 43 44 45 10 
                bytes:[288]
       7085    1048724       1820     524432: 
                bytes:[288]
       5461     524336       5461     524336: 1 76 77 78 79 80 81 82 83 84 30 10 
                bytes:[96]
        512     524800          0          0: 1 2 3 4 46 47 85 86 87 10 
                bytes:[1024]
      32768     524296          0          0: 1 88 89 90 91 92 93 94 95 96 97 98 99 100 10 
                bytes:[16]
Locations
     1: 0x420cee M=1 
     2: 0x422150 M=1 
     3: 0x4221d9 M=1 
     4: 0x41dc0c M=1 
     5: 0x41db47 M=1 
     6: 0x74920e M=1 
     7: 0x6295ab M=1 
     8: 0x629854 M=1 
     9: 0x462768 M=1 
    10: 0x41931f M=1 
    11: 0x63963e M=1 
    12: 0x451a38 M=1 
    13: 0x451ba4 M=1 
    14: 0x450682 M=1 
    15: 0x450076 M=1 
    16: 0x4525a3 M=1 
    17: 0x58e033 M=1 
    18: 0x4524d3 M=1 
    19: 0x40108f M=1 
    20: 0x4011a0 M=1 
    21: 0x416dfe M=1 
    22: 0x477636 M=1 
    23: 0x47718a M=1 
    24: 0x477055 M=1 
    25: 0x4799b1 M=1 
    26: 0x46bfd6 M=1 
    27: 0x526125 M=1 
    28: 0x5261e9 M=1 
    29: 0x4683d3 M=1 
    30: 0x467e08 M=1 
    31: 0x53fbf2 M=1 
    32: 0x53f85e M=1 
    33: 0x545f51 M=1 
    34: 0x545a6f M=1 
    35: 0x420fa8 M=1 
    36: 0x414b21 M=1 
    37: 0x414d1f M=1 
    38: 0x4901bd M=1 
    39: 0x422081 M=1 
    40: 0x48dbe2 M=1 
    41: 0x48d15b M=1 
    42: 0x48cdcf M=1 
    43: 0x4a9dbf M=1 
    44: 0x545bfd M=1 
    45: 0x543ac6 M=1 
    46: 0x41dd67 M=1 
    47: 0x41dcbc M=1 
    48: 0x42914f M=1 
    49: 0x429adc M=1 
    50: 0x42e012 M=1 
    51: 0x4307e1 M=1 
    52: 0x4366fe M=1 
    53: 0x42c1c1 M=1 
    54: 0x653e4c M=1 
    55: 0x64bdc4 M=1 
    56: 0x64c358 M=1 
    57: 0x65a73c M=1 
    58: 0x64cdb0 M=1 
    59: 0x64be72 M=1 
    60: 0x64c599 M=1 
    61: 0x64c204 M=1 
    62: 0x64b777 M=1 
    63: 0x5cd55b M=1 
    64: 0x45dbc2 M=1 
    65: 0x543e6f M=1 
    66: 0x559165 M=1 
    67: 0x55ba53 M=1 
    68: 0x559690 M=1 
    69: 0x559984 M=1 
    70: 0x5a19fe M=1 
    71: 0x419e18 M=1 
    72: 0x41a1a7 M=1 
    73: 0x419f62 M=1 
    74: 0x48f09e M=1 
    75: 0x48d990 M=1 
    76: 0x424f34 M=1 
    77: 0x4255d0 M=1 
    78: 0x6fc292 M=1 
    79: 0x6f9c87 M=1 
    80: 0x6f9943 M=1 
    81: 0x6f96bd M=1 
    82: 0x6f966a M=1 
    83: 0x59f399 M=1 
    84: 0x468317 M=1 
    85: 0x6d71a2 M=1 
    86: 0x6da87c M=1 
    87: 0x7b2c3a M=1 
    88: 0x422047 M=1 
    89: 0x40b516 M=1 
    90: 0x40b745 M=1 
    91: 0x6d9ca1 M=1 
    92: 0x4761c4 M=1 
    93: 0x475ea6 M=1 
    94: 0x46fc4e M=1 
    95: 0x46f17f M=1 
    96: 0x46ef32 M=1 
    97: 0x4ab820 M=1 
    98: 0x4acc31 M=1 
    99: 0x4ac7b6 M=1 
   100: 0x4ace35 M=1 
Mappings
1: 0x0/0xffffffffffffffff/0x0   
