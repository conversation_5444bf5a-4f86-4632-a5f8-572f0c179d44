--- threadz 1 ---

--- Thread 7eff063d9940 (name: main/25376) stack: ---
  PC: 0xbc8f1c 0xbcae55 0xbcb5f5 0x40b688 0x4d5f51 0x40be31 0x7eff052e111d
--- Thread 7eff04b95700 (name: thread1/25380) stack: ---
  PC: 0xbc8f1c 0xbcbd00 0xa47f60 0xa456e4 0x7eff05b6d14e
      creator: 0xa45b96 0xa48928 0xbaa17f 0xbaa9f9 0xbb0d21 0x40bce4 0x7eff052e111d
--- Thread 7eff04893700 (name: thread2/25381) stack: ---
  PC: 0x7eff052dfa93 0x7a1956 0x7a1c45 0x7a2727 0x7a296d 0xa456e4
      0x7eff05b6d14e
      creator: 0xa45b96 0x7a37d2 0x7a3e8d 0xbbff77 0x79ec1c 0x40bd6e 0x7eff052e111d
--- Thread 7eff04770700 (name: thread3/25382) stack: ---
  PC: 0xbc8f1c 0x7a2691 0x7a296d 0xa456e4 0x7eff05b6d14e
      creator: 0xa45b96 0x7a37d2 0x7a3e8d 0xbbff77 0x79ec1c 0x40bd6e 0x7eff052e111d
--- Memory map: ---
  00400000-00fcb000: /home/<USER>/cppbench/cppbench_server_main.unstripped
  7eff04e98000-7eff04e9b000: /lib/libnss_cache-2.15.so
  7eff0509c000-7eff050a8000: /lib/libnss_files-2.15.so
  7eff052a9000-7eff05457000: /lib/libc-2.15.so
  7eff05662000-7eff0575d000: /lib/libm-2.15.so
  7eff0595e000-7eff05965000: /lib/librt-2.15.so
  7eff05b66000-7eff05b7e000: /lib/libpthread-2.15.so
  7eff05d83000-7eff05d86000: /lib/libdl-2.15.so
  7eff05f87000-7eff05f90000: /lib/libcrypt-2.15.so
  7eff061c0000-7eff061e4000: /lib/ld-2.15.so
  7fff2edff000-7fff2ee00000: [vdso]
  ffffffffff600000-ffffffffff601000: [vsyscall]
