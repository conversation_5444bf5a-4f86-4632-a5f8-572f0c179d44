PeriodType: cpu nanoseconds
Period: 10000000
Samples:
samples/count cpu/nanoseconds
          0          0: 1 
          0          0: 2 
          2   20000000: 3 
          1   10000000: 4 5 6 7 8 9 10 11 12 13 14 15 16 17 18 
          1   10000000: 19 20 21 22 23 16 17 18 
          1   10000000: 24 25 26 27 28 29 30 31 32 
          1   10000000: 33 34 35 36 37 38 39 40 41 42 43 44 45 46 47 48 49 50 51 52 53 29 30 31 32 
          1   10000000: 54 55 56 57 58 59 60 61 62 11 63 64 16 17 18 
Locations
     1: 0x0 GC :0:0 s=0
     2: 0x0 Compile :0:0 s=0
     3: 0x0 VM :0:0 s=0
     4: 0x0 com.example.function06 source.java:6:0 s=0
     5: 0x0 com.example.function07 source.java:7:0 s=0
     6: 0x0 com.example.function08 source.java:8:0 s=0
     7: 0x0 com.example.function09 source.java:9:0 s=0
     8: 0x0 com.example.function0a source.java:0:0 s=0
     9: 0x0 com.example.function0b source.java:0:0 s=0
    10: 0x0 com.example.function0c source.java:0:0 s=0
    11: 0x0 com.example.function0d source.java:0:0 s=0
    12: 0x0 com.example.function0e source.java:0:0 s=0
    13: 0x0 com.example.function0f source.java:0:0 s=0
    14: 0x0 com.example.function10 source.java:10:0 s=0
    15: 0x0 com.example.function11 source.java:11:0 s=0
    16: 0x0 com.example.function12 source.java:12:0 s=0
    17: 0x0 com.example.function13 source.java:13:0 s=0
    18: 0x0 com.example.function14 source.java:14:0 s=0
    19: 0x0 com.example.function1d source.java:1:0 s=0
    20: 0x0 com.example.function1e source.java:1:0 s=0
    21: 0x0 com.example.function1f source.java:1:0 s=0
    22: 0x0 com.example.function20 source.java:20:0 s=0
    23: 0x0 com.example.function21 source.java:21:0 s=0
    24: 0x0 com.example.function22 source.java:22:0 s=0
    25: 0x0 com.example.function23 source.java:23:0 s=0
    26: 0x0 com.example.function24 source.java:24:0 s=0
    27: 0x0 com.example.function25 source.java:25:0 s=0
    28: 0x0 com.example.function26 source.java:26:0 s=0
    29: 0x0 com.example.function27 source.java:27:0 s=0
    30: 0x0 com.example.function28 source.java:28:0 s=0
    31: 0x0 com.example.function29 source.java:29:0 s=0
    32: 0x0 com.example.function2a source.java:2:0 s=0
    33: 0x0 com.example.function2b source.java:2:0 s=0
    34: 0x0 com.example.function2c source.java:2:0 s=0
    35: 0x0 com.example.function2d source.java:2:0 s=0
    36: 0x0 com.example.function2e source.java:2:0 s=0
    37: 0x0 com.example.function2f source.java:2:0 s=0
    38: 0x0 com.example.function30 source.java:30:0 s=0
    39: 0x0 com.example.function31 source.java:31:0 s=0
    40: 0x0 com.example.function32 source.java:32:0 s=0
    41: 0x0 com.example.function33 source.java:33:0 s=0
    42: 0x0 com.example.function34 source.java:34:0 s=0
    43: 0x0 com.example.function35 source.java:35:0 s=0
    44: 0x0 com.example.function36 source.java:36:0 s=0
    45: 0x0 com.example.function37 source.java:37:0 s=0
    46: 0x0 com.example.function38 source.java:38:0 s=0
    47: 0x0 com.example.function39 source.java:39:0 s=0
    48: 0x0 com.example.function3a source.java:3:0 s=0
    49: 0x0 com.example.function3b source.java:3:0 s=0
    50: 0x0 com.example.function3c source.java:3:0 s=0
    51: 0x0 com.example.function3d source.java:3:0 s=0
    52: 0x0 com.example.function3e source.java:3:0 s=0
    53: 0x0 com.example.function3f source.java:3:0 s=0
    54: 0x0 com.example.function40 source.java:40:0 s=0
    55: 0x0 com.example.function41 source.java:41:0 s=0
    56: 0x0 com.example.function42 source.java:42:0 s=0
    57: 0x0 com.example.function43 source.java:43:0 s=0
    58: 0x0 com.example.function44 source.java:44:0 s=0
    59: 0x0 com.example.function45 source.java:45:0 s=0
    60: 0x0 com.example.function46 source.java:46:0 s=0
    61: 0x0 com.example.function47 source.java:47:0 s=0
    62: 0x0 com.example.function48 source.java:48:0 s=0
    63: 0x0 com.example.function49 source.java:49:0 s=0
    64: 0x0 com.example.function4a source.java:4:0 s=0
Mappings
