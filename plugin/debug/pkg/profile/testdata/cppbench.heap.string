PeriodType: space bytes
Period: 524288
Samples:
objects/count space/bytes
         57     528909: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[9216]
       3641     524360: 1 12 13 14 15 16 17 18 19 20 21 22 23 24 25 26 17 27 28 29 30 31 32 33 34 35 36 37 38 39 40 
                bytes:[144]
        227    3727658: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[16384]
        293     525184: 1 41 42 5 6 7 8 9 10 11 
                bytes:[1792]
        283    6976735: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[24576]
        293     525184: 1 43 44 45 46 47 48 49 50 51 52 53 54 
                bytes:[1792]
          1    2198218: 55 56 57 58 59 60 61 62 7 8 9 10 11 
                bytes:[2162688]
      10923     524312: 1 63 64 65 66 67 68 69 70 71 
                bytes:[48]
          2     666237: 1 72 73 74 75 76 77 78 79 7 8 9 10 11 
                bytes:[262144]
       1638     524448: 1 80 81 82 83 84 4 5 6 7 8 9 10 11 
                bytes:[320]
        293     525184: 1 85 86 66 68 87 66 88 89 70 71 
                bytes:[1792]
         51     529424: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[10240]
        417    8553514: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[20480]
       3277     524368: 1 90 91 92 93 94 10 11 
                bytes:[160]
         64     528394: 1 95 96 97 98 7 8 9 10 11 
                bytes:[8192]
         86    1060911: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[12288]
          1    2136279: 55 99 57 58 59 60 61 62 7 8 9 10 11 
                bytes:[2097152]
       1170     524512: 1 100 101 102 103 104 105 106 107 75 108 109 110 31 32 33 34 35 36 37 38 39 40 111 112 113 114 7 8 9 10 
                bytes:[448]
        625   25616628: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[40960]
         79     527623: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[6656]
        222    5914839: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[26624]
        128     526338: 1 115 116 117 118 119 120 121 122 123 124 125 126 127 71 
                bytes:[4096]
       4681     524344: 1 128 84 4 5 6 7 8 9 10 11 
                bytes:[112]
         26     534594: 1 129 130 6 7 8 9 10 11 
                bytes:[20480]
      10923     524312: 1 131 132 66 68 133 66 88 89 70 71 
                bytes:[48]
         64     528394: 1 134 135 136 137 138 98 7 8 9 10 11 
                bytes:[8192]
         17    1115476: 1 95 139 140 141 142 143 144 145 146 147 4 5 6 7 8 9 10 11 
                bytes:[65536]
         64     528394: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[8192]
       1024     524544: 1 148 149 150 104 151 24 25 26 17 27 28 29 30 31 32 33 34 35 36 37 38 39 40 111 112 113 114 7 8 9 
                bytes:[512]
        114     526595: 1 152 153 154 155 156 157 8 9 10 11 
                bytes:[4608]
        379   12439381: 1 2 3 4 5 6 7 8 9 10 11 
                bytes:[32768]
Locations
     1: 0xc635c7 M=1 
     2: 0x42ecc2 M=1 
     3: 0x42e14b M=1 
     4: 0x5261ae M=1 
     5: 0x526ede M=1 
     6: 0x5280aa M=1 
     7: 0x79e809 M=1 
     8: 0x7a251a M=1 
     9: 0x7a296c M=1 
    10: 0xa456e3 M=1 
    11: 0x7f47a54360fd M=7 
    12: 0xa7479a M=1 
    13: 0xb65e6a M=1 
    14: 0xb65f7f M=1 
    15: 0xa6d068 M=1 
    16: 0xa6dc7f M=1 
    17: 0xbbffe5 M=1 
    18: 0xa5dd83 M=1 
    19: 0xa7b7c5 M=1 
    20: 0xaa88d9 M=1 
    21: 0xaa9db1 M=1 
    22: 0xb59bad M=1 
    23: 0xb0c39b M=1 
    24: 0xb145f2 M=1 
    25: 0xb147c9 M=1 
    26: 0xa5dddc M=1 
    27: 0xa5e836 M=1 
    28: 0xa65f93 M=1 
    29: 0x5aac9d M=1 
    30: 0x535525 M=1 
    31: 0x535143 M=1 
    32: 0x5aa467 M=1 
    33: 0x7e3ce6 M=1 
    34: 0x7d13a1 M=1 
    35: 0x7e0d27 M=1 
    36: 0x6ab44f M=1 
    37: 0x538d26 M=1 
    38: 0x5390e7 M=1 
    39: 0x5391e2 M=1 
    40: 0x4e9602 M=1 
    41: 0x51a271 M=1 
    42: 0x524996 M=1 
    43: 0xac959f M=1 
    44: 0xacdc7b M=1 
    45: 0xace07a M=1 
    46: 0xace1ab M=1 
    47: 0xabd0ff M=1 
    48: 0xabe2a8 M=1 
    49: 0x72f52d M=1 
    50: 0x655375 M=1 
    51: 0x6558d2 M=1 
    52: 0x41c710 M=1 
    53: 0xc25cc5 M=1 
    54: 0x40651a M=1 
    55: 0xc63567 M=1 
    56: 0xbc462d M=1 
    57: 0xbc4bb4 M=1 
    58: 0xbc4ed9 M=1 
    59: 0x4a57b7 M=1 
    60: 0x4b152b M=1 
    61: 0x4ae04b M=1 
    62: 0x4ad224 M=1 
    63: 0x7be149 M=1 
    64: 0x7be674 M=1 
    65: 0x6b312c M=1 
    66: 0xbaa17e M=1 
    67: 0xbaa141 M=1 
    68: 0xbaabc5 M=1 
    69: 0xbb092b M=1 
    70: 0x40bce3 M=1 
    71: 0x7f47a4bab11c M=4 
    72: 0x8168ff M=1 
    73: 0x8149fc M=1 
    74: 0x8139f3 M=1 
    75: 0xbbff76 M=1 
    76: 0x81421b M=1 
    77: 0x4ed413 M=1 
    78: 0x4fd706 M=1 
    79: 0x4de2a1 M=1 
    80: 0x721a58 M=1 
    81: 0x43005d M=1 
    82: 0x7382a3 M=1 
    83: 0x43058f M=1 
    84: 0x435424 M=1 
    85: 0x5413af M=1 
    86: 0x541ab1 M=1 
    87: 0x53507b M=1 
    88: 0xbaa9f8 M=1 
    89: 0xbb0d20 M=1 
    90: 0x578704 M=1 
    91: 0x586246 M=1 
    92: 0x592614 M=1 
    93: 0x592744 M=1 
    94: 0x592cb8 M=1 
    95: 0xaaf468 M=1 
    96: 0x52cad6 M=1 
    97: 0x52e89a M=1 
    98: 0x527f31 M=1 
    99: 0xbc463a M=1 
   100: 0xafca3a M=1 
   101: 0xb09b9f M=1 
   102: 0xb09ebf M=1 
   103: 0xb12feb M=1 
   104: 0xb13a91 M=1 
   105: 0xb13c92 M=1 
   106: 0xb13d9c M=1 
   107: 0xa02776 M=1 
   108: 0xa026eb M=1 
   109: 0x5701e1 M=1 
   110: 0x535419 M=1 
   111: 0x4faa16 M=1 
   112: 0x4fc5f5 M=1 
   113: 0x4fd027 M=1 
   114: 0x4fd22f M=1 
   115: 0x75373a M=1 
   116: 0x7eb2d2 M=1 
   117: 0x7ecc86 M=1 
   118: 0x7ece55 M=1 
   119: 0x7ed1cd M=1 
   120: 0x7ed35f M=1 
   121: 0x7edb19 M=1 
   122: 0x7edbb4 M=1 
   123: 0x7d50af M=1 
   124: 0x4b9ba5 M=1 
   125: 0x4b9f61 M=1 
   126: 0x4ba024 M=1 
   127: 0x40bd85 M=1 
   128: 0x430497 M=1 
   129: 0x5a8b91 M=1 
   130: 0x526bfe M=1 
   131: 0x720c2d M=1 
   132: 0x5d35ef M=1 
   133: 0x42f03c M=1 
   134: 0xaaf3e5 M=1 
   135: 0xab0b9f M=1 
   136: 0xab11bd M=1 
   137: 0xab1638 M=1 
   138: 0x52ebdb M=1 
   139: 0xaad4cd M=1 
   140: 0xb66bcc M=1 
   141: 0xb670f1 M=1 
   142: 0xb659b4 M=1 
   143: 0x63689a M=1 
   144: 0x548171 M=1 
   145: 0x520cdb M=1 
   146: 0x521b81 M=1 
   147: 0x5194c8 M=1 
   148: 0xaff129 M=1 
   149: 0xb0b330 M=1 
   150: 0xb12f0f M=1 
   151: 0xb0c442 M=1 
   152: 0x464378 M=1 
   153: 0xa6318c M=1 
   154: 0x7feee8 M=1 
   155: 0x5ab69b M=1 
   156: 0x7b0b25 M=1 
   157: 0x79e819 M=1 
Mappings
1: 0x400000/0xfcb000/0x0 /home/<USER>
2: 0x7f47a4351000/0x7f47a4352000/0x0 /lib/libnss_borg-2.15.so  
3: 0x7f47a4554000/0x7f47a4560000/0x0 /lib/libnss_files-2.15.so  
4: 0x7f47a4b73000/0x7f47a4d21000/0x0 /lib/libc-2.15.so  
5: 0x7f47a4f2b000/0x7f47a5026000/0x0 /lib/libm-2.15.so  
6: 0x7f47a5227000/0x7f47a522e000/0x0 /lib/librt-2.15.so  
7: 0x7f47a542f000/0x7f47a5447000/0x0 /lib/libpthread-2.15.so  
8: 0x7f47a564c000/0x7f47a564e000/0x0 /lib/libdl-2.15.so  
9: 0x7f47a5850000/0x7f47a5859000/0x0 /lib/libcrypt-2.15.so  
10: 0x7f47a5a89000/0x7f47a5aad000/0x0 /lib/ld-2.15.so  
11: 0x7fff63dfe000/0x7fff63e00000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
