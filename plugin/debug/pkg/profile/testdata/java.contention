--- contentionz 1 ---
format = java
resolution = microseconds
sampling period = 100
ms since reset = 6019923
            1     1 @ 0x00000003 0x00000004
           14     1 @ 0x0000000d 0x0000000e 0x0000000f 0x00000010 0x00000011 0x00000012 0x00000013 0x00000014 0x00000017 0x00000018 0x00000019 0x0000001a 0x0000001b 0x0000001c 0x00000014 0x00000029 0x0000002a 0x0000002b 0x0000002c 0x0000002d 0x0000002e 0x0000002f 0x00000030 0x00000031 0x00000032 0x00000033 0x00000034 0x00000035
            2     2 @ 0x00000003 0x00000004
            2     3 @ 0x00000036 0x00000037 0x00000038


 0x0000003 com.example.function03 (source.java:03)
 0x0000004 com.example.function04 (source.java:04)
 0x000000d com.example.function0d (source.java:0)
 0x000000e com.example.function0e (source.java:0)
 0x000000f com.example.function0f (source.java:0)
 0x0000010 com.example.function10 (source.java:10)
 0x0000011 com.example.function11 (source.java:11)
 0x0000012 com.example.function12 (source.java:12)
 0x0000013 com.example.function13 (source.java:13)
 0x0000014 com.example.function14 (source.java:14)
 0x0000017 com.example.function17 (source.java:17)
 0x0000018 com.example.function18 (source.java:18)
 0x0000019 com.example.function19 (source.java:19)
 0x000001a com.example.function1a (source.java:1)
 0x000001b com.example.function1b (source.java:1)
 0x000001c com.example.function1c (source.java:1)
 0x0000029 com.example.function29 (source.java:29)
 0x000002a com.example.function2a (source.java:2)
 0x000002b com.example.function2b (source.java:2)
 0x000002c com.example.function2c (source.java:2)
 0x000002d com.example.function2d (source.java:2)
 0x000002e com.example.function2e (source.java:2)
 0x000002f com.example.function2f (source.java:2)
 0x0000030 com.example.function30 (source.java:30)
 0x0000031 com.example.function31 (source.java:31)
 0x0000032 com.example.function32 (source.java:32)
 0x0000033 com.example.function33 (source.java:33)
 0x0000034 com.example.function34 (source.java:34)
 0x0000035 com.example.function35 (source.java:35)
 0x0000036 com.example.function36 (source.java:36)
 0x0000037 com.example.function37 (source.java:37)
 0x0000038 com.example.function38 (source.java:38)
