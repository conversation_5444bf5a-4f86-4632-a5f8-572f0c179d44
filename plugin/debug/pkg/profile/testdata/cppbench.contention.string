PeriodType: contentions count
Period: 100
Duration: 4h35
Samples:
contentions/count delay/nanoseconds
       2700  608881724: 1 2 3 4 5 6 7 8 9 10 11 12 13 
        100      23992: 1 14 12 13 
        200     179943: 1 15 16 17 18 19 20 21 22 23 9 10 11 12 13 
        100   17778444: 1 15 16 24 18 3 4 5 6 7 8 9 10 11 12 13 
        100      75976: 1 15 16 17 18 25 26 27 28 29 30 31 32 33 34 9 
        300   63568134: 1 35 36 37 38 39 40 6 7 8 9 10 11 12 13 
Locations
     1: 0xbccc96 M=1 
     2: 0xc61201 M=1 
     3: 0x42ed5e M=1 
     4: 0x42edc0 M=1 
     5: 0x42e159 M=1 
     6: 0x5261ae M=1 
     7: 0x526ede M=1 
     8: 0x5280aa M=1 
     9: 0x79e809 M=1 
    10: 0x7a251a M=1 
    11: 0x7a296c M=1 
    12: 0xa456e3 M=1 
    13: 0x7fcdc2ff214d M=7 
    14: 0xa42dc6 M=1 
    15: 0xb82b72 M=1 
    16: 0xb82bca M=1 
    17: 0xb87eaa M=1 
    18: 0xb8814b M=1 
    19: 0x4e969c M=1 
    20: 0x4faa16 M=1 
    21: 0x4fc5f5 M=1 
    22: 0x4fd027 M=1 
    23: 0x4fd22f M=1 
    24: 0xb87f07 M=1 
    25: 0x7aa74b M=1 
    26: 0x7ab843 M=1 
    27: 0x7ab913 M=1 
    28: 0x79e9e8 M=1 
    29: 0x79e325 M=1 
    30: 0x4d299d M=1 
    31: 0x4d4b7a M=1 
    32: 0x4b7be7 M=1 
    33: 0x4b7ff0 M=1 
    34: 0x4d2dad M=1 
    35: 0xb82f0e M=1 
    36: 0xb83002 M=1 
    37: 0xb87d4f M=1 
    38: 0xc635ef M=1 
    39: 0x42ecc2 M=1 
    40: 0x42e14b M=1 
Mappings
1: 0x400000/0xfcb000/0x0 cppbench_server_main  
2: 0x7fcdc231e000/0x7fcdc2321000/0x0 /libnss_cache-2.15.so  
3: 0x7fcdc2522000/0x7fcdc252e000/0x0 /libnss_files-2.15.so  
4: 0x7fcdc272f000/0x7fcdc28dd000/0x0 /libc-2.15.so  
5: 0x7fcdc2ae7000/0x7fcdc2be2000/0x0 /libm-2.15.so  
6: 0x7fcdc2de3000/0x7fcdc2dea000/0x0 /librt-2.15.so  
7: 0x7fcdc2feb000/0x7fcdc3003000/0x0 /libpthread-2.15.so  
8: 0x7fcdc3208000/0x7fcdc320a000/0x0 /libdl-2.15.so  
9: 0x7fcdc340c000/0x7fcdc3415000/0x0 /libcrypt-2.15.so  
10: 0x7fcdc3645000/0x7fcdc3669000/0x0 /ld-2.15.so  
11: 0x7fff86bff000/0x7fff86c00000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
