PeriodType: thread count
Period: 1
Samples:
thread/count
          1: 1 2 3 
          1: 4 5 6 7 8 9 10 11 3 
          2: 1 5 6 12 8 9 10 11 3 
Locations
     1: 0xbc8f1c M=1 
     2: 0x40be30 M=1 
     3: 0x7f7949a9811c M=4 
     4: 0x7f794a32bf7d M=7 
     5: 0x7f794a32414d M=7 
     6: 0xa45b95 M=1 
     7: 0xa460b3 M=1 
     8: 0xbaa17e M=1 
     9: 0xbaa9f8 M=1 
    10: 0xbb0d20 M=1 
    11: 0x40bce3 M=1 
    12: 0xa48927 M=1 
Mappings
1: 0x400000/0xfcb000/0x0 /home/<USER>/cppbench/cppbench_server_main  
2: 0x7f794964f000/0x7f7949652000/0x0 /lib/libnss_cache-2.15.so  
3: 0x7f7949853000/0x7f794985f000/0x0 /lib/libnss_files-2.15.so  
4: 0x7f7949a60000/0x7f7949c0e000/0x0 /lib/libc-2.15.so  
5: 0x7f7949e19000/0x7f7949f14000/0x0 /lib/libm-2.15.so  
6: 0x7f794a115000/0x7f794a11c000/0x0 /lib/librt-2.15.so  
7: 0x7f794a31d000/0x7f794a335000/0x0 /lib/libpthread-2.15.so  
8: 0x7f794a53a000/0x7f794a53d000/0x0 /lib/libdl-2.15.so  
9: 0x7f794a73e000/0x7f794a747000/0x0 /lib/libcrypt-2.15.so  
10: 0x7f794a977000/0x7f794a99b000/0x0 /lib/ld-2.15.so  
11: 0x7fffb8dff000/0x7fffb8e00000/0x0 [vdso]  
12: 0xffffffffff600000/0xffffffffff601000/0x0 [vsyscall]  
