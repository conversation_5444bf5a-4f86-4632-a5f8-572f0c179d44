<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Environment Check</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .input-group {
            margin-bottom: 20px;
        }

        input[type="text"] {
            padding: 8px;
            width: 300px;
            margin-right: 10px;
        }

        button {
            padding: 8px 16px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:hover {
            background-color: #45a049;
        }

        #log {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 10px;
            height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }

        .success {
            color: #28a745;
        }

        .error {
            color: #dc3545;
        }

        .info {
            color: #17a2b8;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Environment Check</h1>
        <div class="input-group">
            <input type="text" id="targetUrl" placeholder="Enter target URL (e.g., http://*************:8080)">
            <button onclick="startCheck()">Start Check</button>
        </div>
        <div id="log"></div>
    </div>

    <script>
        function appendLog(message, type = 'info') {
            const log = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = type;
            entry.textContent = message;
            log.appendChild(entry);
            log.scrollTop = log.scrollHeight;
        }

        function startCheck() {
            const targetUrl = document.getElementById('targetUrl').value;
            if (!targetUrl) {
                appendLog('Please enter a target URL', 'error');
                return;
            }

            // Clear previous log
            document.getElementById('log').innerHTML = '';
            appendLog('Starting environment check...');

            // Create SSE connection
            const eventSource = new EventSource(`/debug/envcheck?target=${encodeURIComponent(targetUrl)}`);

            eventSource.onmessage = function (event) {
                const data = JSON.parse(event.data);
                appendLog(data.message, data.type);

                if (data.type === 'complete') {
                    eventSource.close();
                }
            };

            eventSource.onerror = function (error) {
                appendLog('Connection error occurred', 'error');
                eventSource.close();
            };
        }
    </script>
</body>

</html>