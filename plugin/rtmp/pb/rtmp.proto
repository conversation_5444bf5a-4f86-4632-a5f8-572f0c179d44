syntax = "proto3";
import "google/api/annotations.proto";
//import "google/protobuf/empty.proto";
import "global.proto";
package rtmp;
option go_package="m7s.live/v5/plugin/rtmp/pb";

service api {
  rpc PushOut (PushRequest) returns (global.SuccessResponse) {
    option (google.api.http) = {
      post: "/rtmp/api/push/{streamPath=**}"
      body: "remoteURL"
    };
  }
}

message PushRequest {
  string streamPath = 1;
  string remoteURL = 2;
}
