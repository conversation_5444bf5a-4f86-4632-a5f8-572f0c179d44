package rtmp

import (
	"github.com/deepch/vdk/codec/aacparser"
	"m7s.live/v5/pkg"
	. "m7s.live/v5/pkg"
	"m7s.live/v5/pkg/codec"
	"m7s.live/v5/pkg/util"
)

type AudioFrame pkg.Sample

func (avcc *AudioFrame) Parse(_ *Sample) (err error) {
	old := avcc.ICodecCtx
	reader := avcc.NewReader()
	var b byte
	b, err = reader.ReadByte()
	if err != nil {
		return
	}
	switch b & 0b1111_0000 >> 4 {
	case 7:
		if old == nil {
			var pcma codec.PCMACtx
			pcma.SampleRate = 8000
			pcma.Channels = 1
			pcma.SampleSize = 8
			avcc.ICodecCtx = &pcma
		} else {
			avcc.ICodecCtx = old
		}
	case 8:
		if old == nil {
			var ctx codec.PCMUCtx
			ctx.SampleRate = 8000
			ctx.Channels = 1
			ctx.SampleSize = 8
			avcc.ICodecCtx = &ctx
		} else {
			avcc.ICodecCtx = old
		}
	case 10:
		b, err = reader.ReadByte()
		if err != nil {
			return
		}
		if b == 0 {
			if old == nil || !avcc.Memory.Equal(&old.(*AACCtx).SequenceFrame.Memory) {
				var c AACCtx
				c.AACCtx = &codec.AACCtx{}
				c.SequenceFrame.CopyFrom(&avcc.Memory)
				c.CodecData, err = aacparser.NewCodecDataFromMPEG4AudioConfigBytes(c.SequenceFrame.Buffers[0][2:])
				avcc.ICodecCtx = &c
			} else {
				avcc.ICodecCtx = old
				err = ErrSkip
			}
		} else {
			avcc.ICodecCtx = old
		}
	}
	return
}

func (avcc *AudioFrame) Demux() (err error) {
	reader := avcc.NewReader()
	var result util.Memory
	if avcc.FourCC().Is(codec.FourCC_MP4A) {
		err = reader.Skip(2)
	} else {
		err = reader.Skip(1)
	}
	if err == nil {
		reader.Range(result.AppendOne)
		avcc.Raw = result
	}
	return
}

func (avcc *AudioFrame) Mux(fromBase *Sample) (err error) {
	audioData := fromBase.Raw.(AudioData)
	avcc.InitRecycleIndexes(1)
	switch c := fromBase.GetBase().(type) {
	case *codec.AACCtx:
		if avcc.ICodecCtx == nil {
			ctx := &AACCtx{
				AACCtx: c,
			}
			ctx.SequenceFrame.AppendOne(append([]byte{0xAF, 0x00}, c.ConfigBytes...))
			avcc.ICodecCtx = ctx
		}
		head := avcc.NextN(2)
		head[0], head[1] = 0xAF, 0x01
		avcc.Append(audioData.Buffers...)
	default:
		if avcc.ICodecCtx == nil {
			avcc.ICodecCtx = c
		}
		head := avcc.NextN(1)
		head[0] = byte(ParseAudioCodec(c.FourCC()))<<4 | (1 << 1)
		avcc.Append(audioData.Buffers...)
	}
	return
}
