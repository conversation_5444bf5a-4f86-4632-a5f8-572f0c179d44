// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: mp4.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ReqRecordList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Range         string                 `protobuf:"bytes,2,opt,name=range,proto3" json:"range,omitempty"`
	Start         string                 `protobuf:"bytes,3,opt,name=start,proto3" json:"start,omitempty"`
	End           string                 `protobuf:"bytes,4,opt,name=end,proto3" json:"end,omitempty"`
	PageNum       uint32                 `protobuf:"varint,5,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,6,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Mode          string                 `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
	EventLevel    string                 `protobuf:"bytes,8,opt,name=eventLevel,proto3" json:"eventLevel,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordList) Reset() {
	*x = ReqRecordList{}
	mi := &file_mp4_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordList) ProtoMessage() {}

func (x *ReqRecordList) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordList.ProtoReflect.Descriptor instead.
func (*ReqRecordList) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{0}
}

func (x *ReqRecordList) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordList) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

func (x *ReqRecordList) GetStart() string {
	if x != nil {
		return x.Start
	}
	return ""
}

func (x *ReqRecordList) GetEnd() string {
	if x != nil {
		return x.End
	}
	return ""
}

func (x *ReqRecordList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReqRecordList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReqRecordList) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

func (x *ReqRecordList) GetEventLevel() string {
	if x != nil {
		return x.EventLevel
	}
	return ""
}

type ReqRecordDelete struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Ids           []uint32               `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	StartTime     string                 `protobuf:"bytes,3,opt,name=startTime,proto3" json:"startTime,omitempty"`
	EndTime       string                 `protobuf:"bytes,4,opt,name=endTime,proto3" json:"endTime,omitempty"`
	Range         string                 `protobuf:"bytes,5,opt,name=range,proto3" json:"range,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqRecordDelete) Reset() {
	*x = ReqRecordDelete{}
	mi := &file_mp4_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqRecordDelete) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqRecordDelete) ProtoMessage() {}

func (x *ReqRecordDelete) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqRecordDelete.ProtoReflect.Descriptor instead.
func (*ReqRecordDelete) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{1}
}

func (x *ReqRecordDelete) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqRecordDelete) GetIds() []uint32 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ReqRecordDelete) GetStartTime() string {
	if x != nil {
		return x.StartTime
	}
	return ""
}

func (x *ReqRecordDelete) GetEndTime() string {
	if x != nil {
		return x.EndTime
	}
	return ""
}

func (x *ReqRecordDelete) GetRange() string {
	if x != nil {
		return x.Range
	}
	return ""
}

type ReqEventRecord struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StreamPath     string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	EventId        string                 `protobuf:"bytes,2,opt,name=eventId,proto3" json:"eventId,omitempty"`
	EventName      string                 `protobuf:"bytes,3,opt,name=eventName,proto3" json:"eventName,omitempty"`
	BeforeDuration string                 `protobuf:"bytes,4,opt,name=beforeDuration,proto3" json:"beforeDuration,omitempty"`
	AfterDuration  string                 `protobuf:"bytes,5,opt,name=afterDuration,proto3" json:"afterDuration,omitempty"`
	EventDesc      string                 `protobuf:"bytes,6,opt,name=eventDesc,proto3" json:"eventDesc,omitempty"`
	EventLevel     string                 `protobuf:"bytes,7,opt,name=eventLevel,proto3" json:"eventLevel,omitempty"` //事件级别,0表示重要事件，无法删除且表示无需自动删除,1表示非重要事件,达到自动删除时间后，自动删除
	Fragment       string                 `protobuf:"bytes,8,opt,name=fragment,proto3" json:"fragment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ReqEventRecord) Reset() {
	*x = ReqEventRecord{}
	mi := &file_mp4_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqEventRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqEventRecord) ProtoMessage() {}

func (x *ReqEventRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqEventRecord.ProtoReflect.Descriptor instead.
func (*ReqEventRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{2}
}

func (x *ReqEventRecord) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqEventRecord) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *ReqEventRecord) GetEventName() string {
	if x != nil {
		return x.EventName
	}
	return ""
}

func (x *ReqEventRecord) GetBeforeDuration() string {
	if x != nil {
		return x.BeforeDuration
	}
	return ""
}

func (x *ReqEventRecord) GetAfterDuration() string {
	if x != nil {
		return x.AfterDuration
	}
	return ""
}

func (x *ReqEventRecord) GetEventDesc() string {
	if x != nil {
		return x.EventDesc
	}
	return ""
}

func (x *ReqEventRecord) GetEventLevel() string {
	if x != nil {
		return x.EventLevel
	}
	return ""
}

func (x *ReqEventRecord) GetFragment() string {
	if x != nil {
		return x.Fragment
	}
	return ""
}

type ResponseEventRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          uint32                 `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseEventRecord) Reset() {
	*x = ResponseEventRecord{}
	mi := &file_mp4_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseEventRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseEventRecord) ProtoMessage() {}

func (x *ResponseEventRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseEventRecord.ProtoReflect.Descriptor instead.
func (*ResponseEventRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{3}
}

func (x *ResponseEventRecord) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseEventRecord) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseEventRecord) GetData() uint32 {
	if x != nil {
		return x.Data
	}
	return 0
}

type ReqStartRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Fragment      *durationpb.Duration   `protobuf:"bytes,2,opt,name=fragment,proto3" json:"fragment,omitempty"`
	FilePath      string                 `protobuf:"bytes,3,opt,name=filePath,proto3" json:"filePath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqStartRecord) Reset() {
	*x = ReqStartRecord{}
	mi := &file_mp4_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqStartRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqStartRecord) ProtoMessage() {}

func (x *ReqStartRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqStartRecord.ProtoReflect.Descriptor instead.
func (*ReqStartRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{4}
}

func (x *ReqStartRecord) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *ReqStartRecord) GetFragment() *durationpb.Duration {
	if x != nil {
		return x.Fragment
	}
	return nil
}

func (x *ReqStartRecord) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

type ResponseStartRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          uint64                 `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseStartRecord) Reset() {
	*x = ResponseStartRecord{}
	mi := &file_mp4_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseStartRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseStartRecord) ProtoMessage() {}

func (x *ResponseStartRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseStartRecord.ProtoReflect.Descriptor instead.
func (*ResponseStartRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{5}
}

func (x *ResponseStartRecord) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseStartRecord) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseStartRecord) GetData() uint64 {
	if x != nil {
		return x.Data
	}
	return 0
}

type ReqStopRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StreamPath    string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqStopRecord) Reset() {
	*x = ReqStopRecord{}
	mi := &file_mp4_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqStopRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqStopRecord) ProtoMessage() {}

func (x *ReqStopRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqStopRecord.ProtoReflect.Descriptor instead.
func (*ReqStopRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{6}
}

func (x *ReqStopRecord) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type ResponseStopRecord struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          uint64                 `protobuf:"varint,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseStopRecord) Reset() {
	*x = ResponseStopRecord{}
	mi := &file_mp4_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseStopRecord) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseStopRecord) ProtoMessage() {}

func (x *ResponseStopRecord) ProtoReflect() protoreflect.Message {
	mi := &file_mp4_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseStopRecord.ProtoReflect.Descriptor instead.
func (*ResponseStopRecord) Descriptor() ([]byte, []int) {
	return file_mp4_proto_rawDescGZIP(), []int{7}
}

func (x *ResponseStopRecord) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResponseStopRecord) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ResponseStopRecord) GetData() uint64 {
	if x != nil {
		return x.Data
	}
	return 0
}

var File_mp4_proto protoreflect.FileDescriptor

const file_mp4_proto_rawDesc = "" +
	"\n" +
	"\tmp4.proto\x12\x03mp4\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\fglobal.proto\"\xd7\x01\n" +
	"\rReqRecordList\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x14\n" +
	"\x05range\x18\x02 \x01(\tR\x05range\x12\x14\n" +
	"\x05start\x18\x03 \x01(\tR\x05start\x12\x10\n" +
	"\x03end\x18\x04 \x01(\tR\x03end\x12\x18\n" +
	"\apageNum\x18\x05 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x06 \x01(\rR\bpageSize\x12\x12\n" +
	"\x04mode\x18\a \x01(\tR\x04mode\x12\x1e\n" +
	"\n" +
	"eventLevel\x18\b \x01(\tR\n" +
	"eventLevel\"\x91\x01\n" +
	"\x0fReqRecordDelete\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x10\n" +
	"\x03ids\x18\x02 \x03(\rR\x03ids\x12\x1c\n" +
	"\tstartTime\x18\x03 \x01(\tR\tstartTime\x12\x18\n" +
	"\aendTime\x18\x04 \x01(\tR\aendTime\x12\x14\n" +
	"\x05range\x18\x05 \x01(\tR\x05range\"\x90\x02\n" +
	"\x0eReqEventRecord\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x18\n" +
	"\aeventId\x18\x02 \x01(\tR\aeventId\x12\x1c\n" +
	"\teventName\x18\x03 \x01(\tR\teventName\x12&\n" +
	"\x0ebeforeDuration\x18\x04 \x01(\tR\x0ebeforeDuration\x12$\n" +
	"\rafterDuration\x18\x05 \x01(\tR\rafterDuration\x12\x1c\n" +
	"\teventDesc\x18\x06 \x01(\tR\teventDesc\x12\x1e\n" +
	"\n" +
	"eventLevel\x18\a \x01(\tR\n" +
	"eventLevel\x12\x1a\n" +
	"\bfragment\x18\b \x01(\tR\bfragment\"W\n" +
	"\x13ResponseEventRecord\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\rR\x04data\"\x83\x01\n" +
	"\x0eReqStartRecord\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x125\n" +
	"\bfragment\x18\x02 \x01(\v2\x19.google.protobuf.DurationR\bfragment\x12\x1a\n" +
	"\bfilePath\x18\x03 \x01(\tR\bfilePath\"W\n" +
	"\x13ResponseStartRecord\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\x04R\x04data\"/\n" +
	"\rReqStopRecord\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\"V\n" +
	"\x12ResponseStopRecord\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x12\n" +
	"\x04data\x18\x03 \x01(\x04R\x04data2\xca\x04\n" +
	"\x03api\x12]\n" +
	"\x04List\x12\x12.mp4.ReqRecordList\x1a\x1a.global.RecordResponseList\"%\x82\xd3\xe4\x93\x02\x1f\x12\x1d/mp4/api/list/{streamPath=**}\x12T\n" +
	"\aCatalog\x12\x16.google.protobuf.Empty\x1a\x17.global.ResponseCatalog\"\x18\x82\xd3\xe4\x93\x02\x12\x12\x10/mp4/api/catalog\x12b\n" +
	"\x06Delete\x12\x14.mp4.ReqRecordDelete\x1a\x16.global.ResponseDelete\"*\x82\xd3\xe4\x93\x02$:\x01*\"\x1f/mp4/api/delete/{streamPath=**}\x12\\\n" +
	"\n" +
	"EventStart\x12\x13.mp4.ReqEventRecord\x1a\x18.mp4.ResponseEventRecord\"\x1f\x82\xd3\xe4\x93\x02\x19:\x01*\"\x14/mp4/api/event/start\x12g\n" +
	"\vStartRecord\x12\x13.mp4.ReqStartRecord\x1a\x18.mp4.ResponseStartRecord\")\x82\xd3\xe4\x93\x02#:\x01*\"\x1e/mp4/api/start/{streamPath=**}\x12c\n" +
	"\n" +
	"StopRecord\x12\x12.mp4.ReqStopRecord\x1a\x17.mp4.ResponseStopRecord\"(\x82\xd3\xe4\x93\x02\":\x01*\"\x1d/mp4/api/stop/{streamPath=**}B\x1bZ\x19m7s.live/v5/plugin/mp4/pbb\x06proto3"

var (
	file_mp4_proto_rawDescOnce sync.Once
	file_mp4_proto_rawDescData []byte
)

func file_mp4_proto_rawDescGZIP() []byte {
	file_mp4_proto_rawDescOnce.Do(func() {
		file_mp4_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_mp4_proto_rawDesc), len(file_mp4_proto_rawDesc)))
	})
	return file_mp4_proto_rawDescData
}

var file_mp4_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_mp4_proto_goTypes = []any{
	(*ReqRecordList)(nil),         // 0: mp4.ReqRecordList
	(*ReqRecordDelete)(nil),       // 1: mp4.ReqRecordDelete
	(*ReqEventRecord)(nil),        // 2: mp4.ReqEventRecord
	(*ResponseEventRecord)(nil),   // 3: mp4.ResponseEventRecord
	(*ReqStartRecord)(nil),        // 4: mp4.ReqStartRecord
	(*ResponseStartRecord)(nil),   // 5: mp4.ResponseStartRecord
	(*ReqStopRecord)(nil),         // 6: mp4.ReqStopRecord
	(*ResponseStopRecord)(nil),    // 7: mp4.ResponseStopRecord
	(*durationpb.Duration)(nil),   // 8: google.protobuf.Duration
	(*emptypb.Empty)(nil),         // 9: google.protobuf.Empty
	(*pb.RecordResponseList)(nil), // 10: global.RecordResponseList
	(*pb.ResponseCatalog)(nil),    // 11: global.ResponseCatalog
	(*pb.ResponseDelete)(nil),     // 12: global.ResponseDelete
}
var file_mp4_proto_depIdxs = []int32{
	8,  // 0: mp4.ReqStartRecord.fragment:type_name -> google.protobuf.Duration
	0,  // 1: mp4.api.List:input_type -> mp4.ReqRecordList
	9,  // 2: mp4.api.Catalog:input_type -> google.protobuf.Empty
	1,  // 3: mp4.api.Delete:input_type -> mp4.ReqRecordDelete
	2,  // 4: mp4.api.EventStart:input_type -> mp4.ReqEventRecord
	4,  // 5: mp4.api.StartRecord:input_type -> mp4.ReqStartRecord
	6,  // 6: mp4.api.StopRecord:input_type -> mp4.ReqStopRecord
	10, // 7: mp4.api.List:output_type -> global.RecordResponseList
	11, // 8: mp4.api.Catalog:output_type -> global.ResponseCatalog
	12, // 9: mp4.api.Delete:output_type -> global.ResponseDelete
	3,  // 10: mp4.api.EventStart:output_type -> mp4.ResponseEventRecord
	5,  // 11: mp4.api.StartRecord:output_type -> mp4.ResponseStartRecord
	7,  // 12: mp4.api.StopRecord:output_type -> mp4.ResponseStopRecord
	7,  // [7:13] is the sub-list for method output_type
	1,  // [1:7] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_mp4_proto_init() }
func file_mp4_proto_init() {
	if File_mp4_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_mp4_proto_rawDesc), len(file_mp4_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_mp4_proto_goTypes,
		DependencyIndexes: file_mp4_proto_depIdxs,
		MessageInfos:      file_mp4_proto_msgTypes,
	}.Build()
	File_mp4_proto = out.File
	file_mp4_proto_goTypes = nil
	file_mp4_proto_depIdxs = nil
}
