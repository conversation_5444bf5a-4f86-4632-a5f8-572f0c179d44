syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/duration.proto";
import "global.proto";
package mp4;
option go_package="m7s.live/v5/plugin/mp4/pb";

service api {
  rpc List (ReqRecordList) returns (global.RecordResponseList) {
    option (google.api.http) = {
      get: "/mp4/api/list/{streamPath=**}"
    };
  }
  rpc Catalog (google.protobuf.Empty) returns (global.ResponseCatalog) {
    option (google.api.http) = {
      get: "/mp4/api/catalog"
    };
  }
  rpc Delete (ReqRecordDelete) returns (global.ResponseDelete) {
    option (google.api.http) = {
      post: "/mp4/api/delete/{streamPath=**}"
      body: "*"
    };
  }
  rpc EventStart (ReqEventRecord) returns (ResponseEventRecord) {
    option (google.api.http) = {
      post: "/mp4/api/event/start"
      body: "*"
    };
  }
  rpc StartRecord (ReqStartRecord) returns (ResponseStartRecord) {
    option (google.api.http) = {
      post: "/mp4/api/start/{streamPath=**}"
      body: "*"
    };
  }
  rpc StopRecord (ReqStopRecord) returns (ResponseStopRecord) {
    option (google.api.http) = {
      post: "/mp4/api/stop/{streamPath=**}"
      body: "*"
    };
  }
}

message ReqRecordList {
  string streamPath = 1;
  string range = 2;
  string start = 3;
  string end = 4;
  uint32 pageNum = 5;
  uint32 pageSize = 6;
  string mode = 7;
  string eventLevel = 8;
}

message ReqRecordDelete {
  string streamPath = 1;
  repeated uint32 ids = 2;
  string startTime = 3;
  string endTime = 4;
  string range = 5;
}

message ReqEventRecord {
  string streamPath = 1;
  string eventId = 2;
  string eventName = 3;
  string beforeDuration = 4;
  string afterDuration = 5;
  string eventDesc = 6;
  string eventLevel = 7;//事件级别,0表示重要事件，无法删除且表示无需自动删除,1表示非重要事件,达到自动删除时间后，自动删除
  string fragment = 8;
}

message ResponseEventRecord {
  int32 code = 1;
  string message = 2;
  uint32 data = 3;
}

message ReqStartRecord {
  string streamPath = 1;
  google.protobuf.Duration fragment = 2;
  string filePath = 3;
}

message ResponseStartRecord {
  int32 code = 1;
  string message = 2;
  uint64 data = 3;
}

message ReqStopRecord {
  string streamPath = 1;
}

message ResponseStopRecord {
  int32 code = 1;
  string message = 2;
  uint64 data = 3;
}