// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: crontab.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PlanResponseList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TotalCount    uint32                 `protobuf:"varint,3,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
	PageNum       uint32                 `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*Plan                `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlanResponseList) Reset() {
	*x = PlanResponseList{}
	mi := &file_crontab_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanResponseList) ProtoMessage() {}

func (x *PlanResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanResponseList.ProtoReflect.Descriptor instead.
func (*PlanResponseList) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{0}
}

func (x *PlanResponseList) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PlanResponseList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *PlanResponseList) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *PlanResponseList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *PlanResponseList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *PlanResponseList) GetData() []*Plan {
	if x != nil {
		return x.Data
	}
	return nil
}

type Plan struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Enable        bool                   `protobuf:"varint,3,opt,name=enable,proto3" json:"enable,omitempty"`
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=createTime,proto3" json:"createTime,omitempty"`
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=updateTime,proto3" json:"updateTime,omitempty"`
	Plan          string                 `protobuf:"bytes,6,opt,name=plan,proto3" json:"plan,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Plan) Reset() {
	*x = Plan{}
	mi := &file_crontab_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Plan) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Plan) ProtoMessage() {}

func (x *Plan) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Plan.ProtoReflect.Descriptor instead.
func (*Plan) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{1}
}

func (x *Plan) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Plan) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Plan) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *Plan) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Plan) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Plan) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

type ReqPlanList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageNum       uint32                 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqPlanList) Reset() {
	*x = ReqPlanList{}
	mi := &file_crontab_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqPlanList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPlanList) ProtoMessage() {}

func (x *ReqPlanList) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPlanList.ProtoReflect.Descriptor instead.
func (*ReqPlanList) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{2}
}

func (x *ReqPlanList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReqPlanList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type DeleteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRequest) Reset() {
	*x = DeleteRequest{}
	mi := &file_crontab_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRequest) ProtoMessage() {}

func (x *DeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRequest.ProtoReflect.Descriptor instead.
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteRequest) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

type Response struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Response) Reset() {
	*x = Response{}
	mi := &file_crontab_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Response) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Response) ProtoMessage() {}

func (x *Response) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Response.ProtoReflect.Descriptor instead.
func (*Response) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{4}
}

func (x *Response) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *Response) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// RecordPlanStream 相关消息定义
type PlanStream struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanId        uint32                 `protobuf:"varint,1,opt,name=planId,proto3" json:"planId,omitempty"`
	StreamPath    string                 `protobuf:"bytes,2,opt,name=stream_path,json=streamPath,proto3" json:"stream_path,omitempty"`
	Fragment      string                 `protobuf:"bytes,3,opt,name=fragment,proto3" json:"fragment,omitempty"`
	FilePath      string                 `protobuf:"bytes,4,opt,name=filePath,proto3" json:"filePath,omitempty"`
	RecordType    string                 `protobuf:"bytes,5,opt,name=record_type,json=recordType,proto3" json:"record_type,omitempty"` // 录制类型，例如 "mp4", "flv"
	CreatedAt     *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt     *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Enable        bool                   `protobuf:"varint,8,opt,name=enable,proto3" json:"enable,omitempty"` // 是否启用该录制流
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlanStream) Reset() {
	*x = PlanStream{}
	mi := &file_crontab_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlanStream) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlanStream) ProtoMessage() {}

func (x *PlanStream) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlanStream.ProtoReflect.Descriptor instead.
func (*PlanStream) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{5}
}

func (x *PlanStream) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *PlanStream) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *PlanStream) GetFragment() string {
	if x != nil {
		return x.Fragment
	}
	return ""
}

func (x *PlanStream) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *PlanStream) GetRecordType() string {
	if x != nil {
		return x.RecordType
	}
	return ""
}

func (x *PlanStream) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

func (x *PlanStream) GetUpdatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdatedAt
	}
	return nil
}

func (x *PlanStream) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

type ReqPlanStreamList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageNum       uint32                 `protobuf:"varint,1,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	PlanId        uint32                 `protobuf:"varint,3,opt,name=planId,proto3" json:"planId,omitempty"`                          // 可选的按录制计划ID筛选
	StreamPath    string                 `protobuf:"bytes,4,opt,name=stream_path,json=streamPath,proto3" json:"stream_path,omitempty"` // 可选的按流路径筛选
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ReqPlanStreamList) Reset() {
	*x = ReqPlanStreamList{}
	mi := &file_crontab_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ReqPlanStreamList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReqPlanStreamList) ProtoMessage() {}

func (x *ReqPlanStreamList) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReqPlanStreamList.ProtoReflect.Descriptor instead.
func (*ReqPlanStreamList) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{6}
}

func (x *ReqPlanStreamList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *ReqPlanStreamList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ReqPlanStreamList) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *ReqPlanStreamList) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

type RecordPlanStreamResponseList struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	TotalCount    uint32                 `protobuf:"varint,3,opt,name=totalCount,proto3" json:"totalCount,omitempty"`
	PageNum       uint32                 `protobuf:"varint,4,opt,name=pageNum,proto3" json:"pageNum,omitempty"`
	PageSize      uint32                 `protobuf:"varint,5,opt,name=pageSize,proto3" json:"pageSize,omitempty"`
	Data          []*PlanStream          `protobuf:"bytes,6,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RecordPlanStreamResponseList) Reset() {
	*x = RecordPlanStreamResponseList{}
	mi := &file_crontab_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RecordPlanStreamResponseList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RecordPlanStreamResponseList) ProtoMessage() {}

func (x *RecordPlanStreamResponseList) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RecordPlanStreamResponseList.ProtoReflect.Descriptor instead.
func (*RecordPlanStreamResponseList) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{7}
}

func (x *RecordPlanStreamResponseList) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RecordPlanStreamResponseList) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *RecordPlanStreamResponseList) GetTotalCount() uint32 {
	if x != nil {
		return x.TotalCount
	}
	return 0
}

func (x *RecordPlanStreamResponseList) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

func (x *RecordPlanStreamResponseList) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *RecordPlanStreamResponseList) GetData() []*PlanStream {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeletePlanStreamRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PlanId        uint32                 `protobuf:"varint,1,opt,name=planId,proto3" json:"planId,omitempty"`
	StreamPath    string                 `protobuf:"bytes,2,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePlanStreamRequest) Reset() {
	*x = DeletePlanStreamRequest{}
	mi := &file_crontab_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePlanStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePlanStreamRequest) ProtoMessage() {}

func (x *DeletePlanStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePlanStreamRequest.ProtoReflect.Descriptor instead.
func (*DeletePlanStreamRequest) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{8}
}

func (x *DeletePlanStreamRequest) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *DeletePlanStreamRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

// 解析计划请求
type ParsePlanRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Plan          string                 `protobuf:"bytes,1,opt,name=plan,proto3" json:"plan,omitempty"` // 168位的0/1字符串，表示一周的每个小时是否录制
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParsePlanRequest) Reset() {
	*x = ParsePlanRequest{}
	mi := &file_crontab_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParsePlanRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsePlanRequest) ProtoMessage() {}

func (x *ParsePlanRequest) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsePlanRequest.ProtoReflect.Descriptor instead.
func (*ParsePlanRequest) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{9}
}

func (x *ParsePlanRequest) GetPlan() string {
	if x != nil {
		return x.Plan
	}
	return ""
}

// 时间段信息
type TimeSlotInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Start         *timestamppb.Timestamp `protobuf:"bytes,1,opt,name=start,proto3" json:"start,omitempty"`                          // 开始时间
	End           *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=end,proto3" json:"end,omitempty"`                              // 结束时间
	Weekday       string                 `protobuf:"bytes,3,opt,name=weekday,proto3" json:"weekday,omitempty"`                      // 周几（例如：周一）
	TimeRange     string                 `protobuf:"bytes,4,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"` // 时间范围（例如：09:00-10:00）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TimeSlotInfo) Reset() {
	*x = TimeSlotInfo{}
	mi := &file_crontab_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TimeSlotInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeSlotInfo) ProtoMessage() {}

func (x *TimeSlotInfo) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeSlotInfo.ProtoReflect.Descriptor instead.
func (*TimeSlotInfo) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{10}
}

func (x *TimeSlotInfo) GetStart() *timestamppb.Timestamp {
	if x != nil {
		return x.Start
	}
	return nil
}

func (x *TimeSlotInfo) GetEnd() *timestamppb.Timestamp {
	if x != nil {
		return x.End
	}
	return nil
}

func (x *TimeSlotInfo) GetWeekday() string {
	if x != nil {
		return x.Weekday
	}
	return ""
}

func (x *TimeSlotInfo) GetTimeRange() string {
	if x != nil {
		return x.TimeRange
	}
	return ""
}

// 解析计划响应
type ParsePlanResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                        // 响应码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                   // 响应消息
	Slots         []*TimeSlotInfo        `protobuf:"bytes,3,rep,name=slots,proto3" json:"slots,omitempty"`                       // 所有计划的时间段
	NextSlot      *TimeSlotInfo          `protobuf:"bytes,4,opt,name=next_slot,json=nextSlot,proto3" json:"next_slot,omitempty"` // 从当前时间开始的下一个时间段
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ParsePlanResponse) Reset() {
	*x = ParsePlanResponse{}
	mi := &file_crontab_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ParsePlanResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ParsePlanResponse) ProtoMessage() {}

func (x *ParsePlanResponse) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ParsePlanResponse.ProtoReflect.Descriptor instead.
func (*ParsePlanResponse) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{11}
}

func (x *ParsePlanResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ParsePlanResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ParsePlanResponse) GetSlots() []*TimeSlotInfo {
	if x != nil {
		return x.Slots
	}
	return nil
}

func (x *ParsePlanResponse) GetNextSlot() *TimeSlotInfo {
	if x != nil {
		return x.NextSlot
	}
	return nil
}

// 新增的消息定义
// 获取Crontab状态请求
type CrontabStatusRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 可以为空，表示获取所有任务
	StreamPath    string `protobuf:"bytes,1,opt,name=stream_path,json=streamPath,proto3" json:"stream_path,omitempty"` // 可选，按流路径过滤
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CrontabStatusRequest) Reset() {
	*x = CrontabStatusRequest{}
	mi := &file_crontab_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrontabStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrontabStatusRequest) ProtoMessage() {}

func (x *CrontabStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrontabStatusRequest.ProtoReflect.Descriptor instead.
func (*CrontabStatusRequest) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{12}
}

func (x *CrontabStatusRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

// 任务信息
type CrontabTaskInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	PlanId           uint32                 `protobuf:"varint,1,opt,name=plan_id,json=planId,proto3" json:"plan_id,omitempty"`                                // 计划ID
	PlanName         string                 `protobuf:"bytes,2,opt,name=plan_name,json=planName,proto3" json:"plan_name,omitempty"`                           // 计划名称
	StreamPath       string                 `protobuf:"bytes,3,opt,name=stream_path,json=streamPath,proto3" json:"stream_path,omitempty"`                     // 流路径
	IsRecording      bool                   `protobuf:"varint,4,opt,name=is_recording,json=isRecording,proto3" json:"is_recording,omitempty"`                 // 是否正在录制
	StartTime        *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`                        // 当前/下一个任务开始时间
	EndTime          *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`                              // 当前/下一个任务结束时间
	TimeRange        string                 `protobuf:"bytes,7,opt,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`                        // 时间范围（例如：09:00-10:00）
	Weekday          string                 `protobuf:"bytes,8,opt,name=weekday,proto3" json:"weekday,omitempty"`                                             // 周几（例如：周一）
	FilePath         string                 `protobuf:"bytes,9,opt,name=file_path,json=filePath,proto3" json:"file_path,omitempty"`                           // 文件保存路径
	Fragment         string                 `protobuf:"bytes,10,opt,name=fragment,proto3" json:"fragment,omitempty"`                                          // 分片设置
	ElapsedSeconds   uint32                 `protobuf:"varint,11,opt,name=elapsed_seconds,json=elapsedSeconds,proto3" json:"elapsed_seconds,omitempty"`       // 已运行时间（秒，仅对正在运行的任务有效）
	RemainingSeconds uint32                 `protobuf:"varint,12,opt,name=remaining_seconds,json=remainingSeconds,proto3" json:"remaining_seconds,omitempty"` // 剩余时间（秒）
	PlanSlots        []*TimeSlotInfo        `protobuf:"bytes,13,rep,name=plan_slots,json=planSlots,proto3" json:"plan_slots,omitempty"`                       // 完整的计划时间段列表
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CrontabTaskInfo) Reset() {
	*x = CrontabTaskInfo{}
	mi := &file_crontab_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrontabTaskInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrontabTaskInfo) ProtoMessage() {}

func (x *CrontabTaskInfo) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrontabTaskInfo.ProtoReflect.Descriptor instead.
func (*CrontabTaskInfo) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{13}
}

func (x *CrontabTaskInfo) GetPlanId() uint32 {
	if x != nil {
		return x.PlanId
	}
	return 0
}

func (x *CrontabTaskInfo) GetPlanName() string {
	if x != nil {
		return x.PlanName
	}
	return ""
}

func (x *CrontabTaskInfo) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *CrontabTaskInfo) GetIsRecording() bool {
	if x != nil {
		return x.IsRecording
	}
	return false
}

func (x *CrontabTaskInfo) GetStartTime() *timestamppb.Timestamp {
	if x != nil {
		return x.StartTime
	}
	return nil
}

func (x *CrontabTaskInfo) GetEndTime() *timestamppb.Timestamp {
	if x != nil {
		return x.EndTime
	}
	return nil
}

func (x *CrontabTaskInfo) GetTimeRange() string {
	if x != nil {
		return x.TimeRange
	}
	return ""
}

func (x *CrontabTaskInfo) GetWeekday() string {
	if x != nil {
		return x.Weekday
	}
	return ""
}

func (x *CrontabTaskInfo) GetFilePath() string {
	if x != nil {
		return x.FilePath
	}
	return ""
}

func (x *CrontabTaskInfo) GetFragment() string {
	if x != nil {
		return x.Fragment
	}
	return ""
}

func (x *CrontabTaskInfo) GetElapsedSeconds() uint32 {
	if x != nil {
		return x.ElapsedSeconds
	}
	return 0
}

func (x *CrontabTaskInfo) GetRemainingSeconds() uint32 {
	if x != nil {
		return x.RemainingSeconds
	}
	return 0
}

func (x *CrontabTaskInfo) GetPlanSlots() []*TimeSlotInfo {
	if x != nil {
		return x.PlanSlots
	}
	return nil
}

// 获取Crontab状态响应
type CrontabStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`                                     // 响应码
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`                                // 响应消息
	RunningTasks  []*CrontabTaskInfo     `protobuf:"bytes,3,rep,name=running_tasks,json=runningTasks,proto3" json:"running_tasks,omitempty"`  // 当前正在执行的任务列表
	NextTasks     []*CrontabTaskInfo     `protobuf:"bytes,4,rep,name=next_tasks,json=nextTasks,proto3" json:"next_tasks,omitempty"`           // 下一个计划执行的任务列表
	TotalRunning  uint32                 `protobuf:"varint,5,opt,name=total_running,json=totalRunning,proto3" json:"total_running,omitempty"` // 正在运行的任务总数
	TotalPlanned  uint32                 `protobuf:"varint,6,opt,name=total_planned,json=totalPlanned,proto3" json:"total_planned,omitempty"` // 计划中的任务总数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CrontabStatusResponse) Reset() {
	*x = CrontabStatusResponse{}
	mi := &file_crontab_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CrontabStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CrontabStatusResponse) ProtoMessage() {}

func (x *CrontabStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_crontab_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CrontabStatusResponse.ProtoReflect.Descriptor instead.
func (*CrontabStatusResponse) Descriptor() ([]byte, []int) {
	return file_crontab_proto_rawDescGZIP(), []int{14}
}

func (x *CrontabStatusResponse) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CrontabStatusResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *CrontabStatusResponse) GetRunningTasks() []*CrontabTaskInfo {
	if x != nil {
		return x.RunningTasks
	}
	return nil
}

func (x *CrontabStatusResponse) GetNextTasks() []*CrontabTaskInfo {
	if x != nil {
		return x.NextTasks
	}
	return nil
}

func (x *CrontabStatusResponse) GetTotalRunning() uint32 {
	if x != nil {
		return x.TotalRunning
	}
	return 0
}

func (x *CrontabStatusResponse) GetTotalPlanned() uint32 {
	if x != nil {
		return x.TotalPlanned
	}
	return 0
}

var File_crontab_proto protoreflect.FileDescriptor

const file_crontab_proto_rawDesc = "" +
	"\n" +
	"\rcrontab.proto\x12\acrontab\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb9\x01\n" +
	"\x10PlanResponseList\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"totalCount\x18\x03 \x01(\rR\n" +
	"totalCount\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\rR\bpageSize\x12!\n" +
	"\x04data\x18\x06 \x03(\v2\r.crontab.PlanR\x04data\"\xce\x01\n" +
	"\x04Plan\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06enable\x18\x03 \x01(\bR\x06enable\x12:\n" +
	"\n" +
	"createTime\x18\x04 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12:\n" +
	"\n" +
	"updateTime\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12\x12\n" +
	"\x04plan\x18\x06 \x01(\tR\x04plan\"C\n" +
	"\vReqPlanList\x12\x18\n" +
	"\apageNum\x18\x01 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\rR\bpageSize\"\x1f\n" +
	"\rDeleteRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\"8\n" +
	"\bResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xac\x02\n" +
	"\n" +
	"PlanStream\x12\x16\n" +
	"\x06planId\x18\x01 \x01(\rR\x06planId\x12\x1f\n" +
	"\vstream_path\x18\x02 \x01(\tR\n" +
	"streamPath\x12\x1a\n" +
	"\bfragment\x18\x03 \x01(\tR\bfragment\x12\x1a\n" +
	"\bfilePath\x18\x04 \x01(\tR\bfilePath\x12\x1f\n" +
	"\vrecord_type\x18\x05 \x01(\tR\n" +
	"recordType\x129\n" +
	"\n" +
	"created_at\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\tcreatedAt\x129\n" +
	"\n" +
	"updated_at\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\tupdatedAt\x12\x16\n" +
	"\x06enable\x18\b \x01(\bR\x06enable\"\x82\x01\n" +
	"\x11ReqPlanStreamList\x12\x18\n" +
	"\apageNum\x18\x01 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\rR\bpageSize\x12\x16\n" +
	"\x06planId\x18\x03 \x01(\rR\x06planId\x12\x1f\n" +
	"\vstream_path\x18\x04 \x01(\tR\n" +
	"streamPath\"\xcb\x01\n" +
	"\x1cRecordPlanStreamResponseList\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\x1e\n" +
	"\n" +
	"totalCount\x18\x03 \x01(\rR\n" +
	"totalCount\x12\x18\n" +
	"\apageNum\x18\x04 \x01(\rR\apageNum\x12\x1a\n" +
	"\bpageSize\x18\x05 \x01(\rR\bpageSize\x12'\n" +
	"\x04data\x18\x06 \x03(\v2\x13.crontab.PlanStreamR\x04data\"Q\n" +
	"\x17DeletePlanStreamRequest\x12\x16\n" +
	"\x06planId\x18\x01 \x01(\rR\x06planId\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x02 \x01(\tR\n" +
	"streamPath\"&\n" +
	"\x10ParsePlanRequest\x12\x12\n" +
	"\x04plan\x18\x01 \x01(\tR\x04plan\"\xa7\x01\n" +
	"\fTimeSlotInfo\x120\n" +
	"\x05start\x18\x01 \x01(\v2\x1a.google.protobuf.TimestampR\x05start\x12,\n" +
	"\x03end\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampR\x03end\x12\x18\n" +
	"\aweekday\x18\x03 \x01(\tR\aweekday\x12\x1d\n" +
	"\n" +
	"time_range\x18\x04 \x01(\tR\ttimeRange\"\xa2\x01\n" +
	"\x11ParsePlanResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12+\n" +
	"\x05slots\x18\x03 \x03(\v2\x15.crontab.TimeSlotInfoR\x05slots\x122\n" +
	"\tnext_slot\x18\x04 \x01(\v2\x15.crontab.TimeSlotInfoR\bnextSlot\"7\n" +
	"\x14CrontabStatusRequest\x12\x1f\n" +
	"\vstream_path\x18\x01 \x01(\tR\n" +
	"streamPath\"\xfb\x03\n" +
	"\x0fCrontabTaskInfo\x12\x17\n" +
	"\aplan_id\x18\x01 \x01(\rR\x06planId\x12\x1b\n" +
	"\tplan_name\x18\x02 \x01(\tR\bplanName\x12\x1f\n" +
	"\vstream_path\x18\x03 \x01(\tR\n" +
	"streamPath\x12!\n" +
	"\fis_recording\x18\x04 \x01(\bR\visRecording\x129\n" +
	"\n" +
	"start_time\x18\x05 \x01(\v2\x1a.google.protobuf.TimestampR\tstartTime\x125\n" +
	"\bend_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\aendTime\x12\x1d\n" +
	"\n" +
	"time_range\x18\a \x01(\tR\ttimeRange\x12\x18\n" +
	"\aweekday\x18\b \x01(\tR\aweekday\x12\x1b\n" +
	"\tfile_path\x18\t \x01(\tR\bfilePath\x12\x1a\n" +
	"\bfragment\x18\n" +
	" \x01(\tR\bfragment\x12'\n" +
	"\x0felapsed_seconds\x18\v \x01(\rR\x0eelapsedSeconds\x12+\n" +
	"\x11remaining_seconds\x18\f \x01(\rR\x10remainingSeconds\x124\n" +
	"\n" +
	"plan_slots\x18\r \x03(\v2\x15.crontab.TimeSlotInfoR\tplanSlots\"\x87\x02\n" +
	"\x15CrontabStatusResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12=\n" +
	"\rrunning_tasks\x18\x03 \x03(\v2\x18.crontab.CrontabTaskInfoR\frunningTasks\x127\n" +
	"\n" +
	"next_tasks\x18\x04 \x03(\v2\x18.crontab.CrontabTaskInfoR\tnextTasks\x12#\n" +
	"\rtotal_running\x18\x05 \x01(\rR\ftotalRunning\x12#\n" +
	"\rtotal_planned\x18\x06 \x01(\rR\ftotalPlanned2\xe0\a\n" +
	"\x03api\x12O\n" +
	"\x04List\x12\x14.crontab.ReqPlanList\x1a\x19.crontab.PlanResponseList\"\x16\x82\xd3\xe4\x93\x02\x10\x12\x0e/plan/api/list\x12A\n" +
	"\x03Add\x12\r.crontab.Plan\x1a\x11.crontab.Response\"\x18\x82\xd3\xe4\x93\x02\x12:\x01*\"\r/plan/api/add\x12L\n" +
	"\x06Update\x12\r.crontab.Plan\x1a\x11.crontab.Response\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/plan/api/update/{id}\x12U\n" +
	"\x06Remove\x12\x16.crontab.DeleteRequest\x1a\x11.crontab.Response\" \x82\xd3\xe4\x93\x02\x1a:\x01*\"\x15/plan/api/remove/{id}\x12x\n" +
	"\x15ListRecordPlanStreams\x12\x1a.crontab.ReqPlanStreamList\x1a%.crontab.RecordPlanStreamResponseList\"\x1c\x82\xd3\xe4\x93\x02\x16\x12\x14/planstream/api/list\x12]\n" +
	"\x13AddRecordPlanStream\x12\x13.crontab.PlanStream\x1a\x11.crontab.Response\"\x1e\x82\xd3\xe4\x93\x02\x18:\x01*\"\x13/planstream/api/add\x12c\n" +
	"\x16UpdateRecordPlanStream\x12\x13.crontab.PlanStream\x1a\x11.crontab.Response\"!\x82\xd3\xe4\x93\x02\x1b:\x01*\"\x16/planstream/api/update\x12\x89\x01\n" +
	"\x16RemoveRecordPlanStream\x12 .crontab.DeletePlanStreamRequest\x1a\x11.crontab.Response\":\x82\xd3\xe4\x93\x024:\x01*\"//planstream/api/remove/{planId}/{streamPath=**}\x12f\n" +
	"\rParsePlanTime\x12\x19.crontab.ParsePlanRequest\x1a\x1a.crontab.ParsePlanResponse\"\x1e\x82\xd3\xe4\x93\x02\x18\x12\x16/plan/api/parse/{plan}\x12n\n" +
	"\x10GetCrontabStatus\x12\x1d.crontab.CrontabStatusRequest\x1a\x1e.crontab.CrontabStatusResponse\"\x1b\x82\xd3\xe4\x93\x02\x15\x12\x13/crontab/api/statusB\x1fZ\x1dm7s.live/v5/plugin/crontab/pbb\x06proto3"

var (
	file_crontab_proto_rawDescOnce sync.Once
	file_crontab_proto_rawDescData []byte
)

func file_crontab_proto_rawDescGZIP() []byte {
	file_crontab_proto_rawDescOnce.Do(func() {
		file_crontab_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_crontab_proto_rawDesc), len(file_crontab_proto_rawDesc)))
	})
	return file_crontab_proto_rawDescData
}

var file_crontab_proto_msgTypes = make([]protoimpl.MessageInfo, 15)
var file_crontab_proto_goTypes = []any{
	(*PlanResponseList)(nil),             // 0: crontab.PlanResponseList
	(*Plan)(nil),                         // 1: crontab.Plan
	(*ReqPlanList)(nil),                  // 2: crontab.ReqPlanList
	(*DeleteRequest)(nil),                // 3: crontab.DeleteRequest
	(*Response)(nil),                     // 4: crontab.Response
	(*PlanStream)(nil),                   // 5: crontab.PlanStream
	(*ReqPlanStreamList)(nil),            // 6: crontab.ReqPlanStreamList
	(*RecordPlanStreamResponseList)(nil), // 7: crontab.RecordPlanStreamResponseList
	(*DeletePlanStreamRequest)(nil),      // 8: crontab.DeletePlanStreamRequest
	(*ParsePlanRequest)(nil),             // 9: crontab.ParsePlanRequest
	(*TimeSlotInfo)(nil),                 // 10: crontab.TimeSlotInfo
	(*ParsePlanResponse)(nil),            // 11: crontab.ParsePlanResponse
	(*CrontabStatusRequest)(nil),         // 12: crontab.CrontabStatusRequest
	(*CrontabTaskInfo)(nil),              // 13: crontab.CrontabTaskInfo
	(*CrontabStatusResponse)(nil),        // 14: crontab.CrontabStatusResponse
	(*timestamppb.Timestamp)(nil),        // 15: google.protobuf.Timestamp
}
var file_crontab_proto_depIdxs = []int32{
	1,  // 0: crontab.PlanResponseList.data:type_name -> crontab.Plan
	15, // 1: crontab.Plan.createTime:type_name -> google.protobuf.Timestamp
	15, // 2: crontab.Plan.updateTime:type_name -> google.protobuf.Timestamp
	15, // 3: crontab.PlanStream.created_at:type_name -> google.protobuf.Timestamp
	15, // 4: crontab.PlanStream.updated_at:type_name -> google.protobuf.Timestamp
	5,  // 5: crontab.RecordPlanStreamResponseList.data:type_name -> crontab.PlanStream
	15, // 6: crontab.TimeSlotInfo.start:type_name -> google.protobuf.Timestamp
	15, // 7: crontab.TimeSlotInfo.end:type_name -> google.protobuf.Timestamp
	10, // 8: crontab.ParsePlanResponse.slots:type_name -> crontab.TimeSlotInfo
	10, // 9: crontab.ParsePlanResponse.next_slot:type_name -> crontab.TimeSlotInfo
	15, // 10: crontab.CrontabTaskInfo.start_time:type_name -> google.protobuf.Timestamp
	15, // 11: crontab.CrontabTaskInfo.end_time:type_name -> google.protobuf.Timestamp
	10, // 12: crontab.CrontabTaskInfo.plan_slots:type_name -> crontab.TimeSlotInfo
	13, // 13: crontab.CrontabStatusResponse.running_tasks:type_name -> crontab.CrontabTaskInfo
	13, // 14: crontab.CrontabStatusResponse.next_tasks:type_name -> crontab.CrontabTaskInfo
	2,  // 15: crontab.api.List:input_type -> crontab.ReqPlanList
	1,  // 16: crontab.api.Add:input_type -> crontab.Plan
	1,  // 17: crontab.api.Update:input_type -> crontab.Plan
	3,  // 18: crontab.api.Remove:input_type -> crontab.DeleteRequest
	6,  // 19: crontab.api.ListRecordPlanStreams:input_type -> crontab.ReqPlanStreamList
	5,  // 20: crontab.api.AddRecordPlanStream:input_type -> crontab.PlanStream
	5,  // 21: crontab.api.UpdateRecordPlanStream:input_type -> crontab.PlanStream
	8,  // 22: crontab.api.RemoveRecordPlanStream:input_type -> crontab.DeletePlanStreamRequest
	9,  // 23: crontab.api.ParsePlanTime:input_type -> crontab.ParsePlanRequest
	12, // 24: crontab.api.GetCrontabStatus:input_type -> crontab.CrontabStatusRequest
	0,  // 25: crontab.api.List:output_type -> crontab.PlanResponseList
	4,  // 26: crontab.api.Add:output_type -> crontab.Response
	4,  // 27: crontab.api.Update:output_type -> crontab.Response
	4,  // 28: crontab.api.Remove:output_type -> crontab.Response
	7,  // 29: crontab.api.ListRecordPlanStreams:output_type -> crontab.RecordPlanStreamResponseList
	4,  // 30: crontab.api.AddRecordPlanStream:output_type -> crontab.Response
	4,  // 31: crontab.api.UpdateRecordPlanStream:output_type -> crontab.Response
	4,  // 32: crontab.api.RemoveRecordPlanStream:output_type -> crontab.Response
	11, // 33: crontab.api.ParsePlanTime:output_type -> crontab.ParsePlanResponse
	14, // 34: crontab.api.GetCrontabStatus:output_type -> crontab.CrontabStatusResponse
	25, // [25:35] is the sub-list for method output_type
	15, // [15:25] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_crontab_proto_init() }
func file_crontab_proto_init() {
	if File_crontab_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_crontab_proto_rawDesc), len(file_crontab_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   15,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_crontab_proto_goTypes,
		DependencyIndexes: file_crontab_proto_depIdxs,
		MessageInfos:      file_crontab_proto_msgTypes,
	}.Build()
	File_crontab_proto = out.File
	file_crontab_proto_goTypes = nil
	file_crontab_proto_depIdxs = nil
}
