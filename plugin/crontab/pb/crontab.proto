syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/timestamp.proto";
package crontab;
option go_package="m7s.live/v5/plugin/crontab/pb";

service api {
  rpc List (ReqPlanList) returns (PlanResponseList) {
    option (google.api.http) = {
      get: "/plan/api/list"
    };
  }
  rpc Add (Plan) returns (Response) {
    option (google.api.http) = {
      post: "/plan/api/add"
      body: "*"
    };
  }
  rpc Update (Plan) returns (Response) {
    option (google.api.http) = {
      post: "/plan/api/update/{id}"
      body: "*"
    };
  }
  rpc Remove (DeleteRequest) returns (Response) {
    option (google.api.http) = {
      post: "/plan/api/remove/{id}"
      body: "*"
    };
  }

  // RecordPlanStream 相关接口
  rpc ListRecordPlanStreams (ReqPlanStreamList) returns (RecordPlanStreamResponseList) {
    option (google.api.http) = {
      get: "/planstream/api/list"
    };
  }
  rpc AddRecordPlanStream (PlanStream) returns (Response) {
    option (google.api.http) = {
      post: "/planstream/api/add"
      body: "*"
    };
  }
  rpc UpdateRecordPlanStream (PlanStream) returns (Response) {
    option (google.api.http) = {
      post: "/planstream/api/update"
      body: "*"
    };
  }
  rpc RemoveRecordPlanStream (DeletePlanStreamRequest) returns (Response) {
    option (google.api.http) = {
      post: "/planstream/api/remove/{planId}/{streamPath=**}"
      body: "*"
    };
  }

  // 解析计划字符串，返回时间段信息
  rpc ParsePlanTime (ParsePlanRequest) returns (ParsePlanResponse) {
    option (google.api.http) = {
      get: "/plan/api/parse/{plan}"
    };
  }
  
  // 获取当前Crontab任务状态
  rpc GetCrontabStatus (CrontabStatusRequest) returns (CrontabStatusResponse) {
    option (google.api.http) = {
      get: "/crontab/api/status"
    };
  }
}

message PlanResponseList {
  int32 code = 1;
  string message = 2;
  uint32 totalCount = 3;
  uint32 pageNum = 4;
  uint32 pageSize = 5;
  repeated Plan data = 6;
}

message Plan {
  uint32 id = 1;
  string name = 2;
  bool enable = 3;
  google.protobuf.Timestamp createTime = 4;
  google.protobuf.Timestamp updateTime = 5;
  string plan = 6;
}

message ReqPlanList {
  uint32 pageNum = 1;
  uint32 pageSize = 2;
}

message DeleteRequest {
  uint32 id = 1;
}

message Response {
  int32 code = 1;
  string message = 2;
}

// RecordPlanStream 相关消息定义
message PlanStream {
  uint32 planId = 1;
  string stream_path = 2;
  string fragment = 3;
  string filePath = 4;
  string record_type = 5; // 录制类型，例如 "mp4", "flv"
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  bool enable = 8; // 是否启用该录制流
}

message ReqPlanStreamList {
  uint32 pageNum = 1;
  uint32 pageSize = 2;
  uint32 planId = 3;  // 可选的按录制计划ID筛选
  string stream_path = 4;     // 可选的按流路径筛选
}

message RecordPlanStreamResponseList {
  int32 code = 1;
  string message = 2;
  uint32 totalCount = 3;
  uint32 pageNum = 4;
  uint32 pageSize = 5;
  repeated PlanStream data = 6;
}

message DeletePlanStreamRequest {
  uint32 planId = 1;
  string streamPath = 2;
}

// 解析计划请求
message ParsePlanRequest {
  string plan = 1;  // 168位的0/1字符串，表示一周的每个小时是否录制
}

// 时间段信息
message TimeSlotInfo {
  google.protobuf.Timestamp start = 1;  // 开始时间
  google.protobuf.Timestamp end = 2;    // 结束时间
  string weekday = 3;                   // 周几（例如：周一）
  string time_range = 4;                // 时间范围（例如：09:00-10:00）
}

// 解析计划响应
message ParsePlanResponse {
  int32 code = 1;                     // 响应码
  string message = 2;                 // 响应消息
  repeated TimeSlotInfo slots = 3;    // 所有计划的时间段
  TimeSlotInfo next_slot = 4;        // 从当前时间开始的下一个时间段
}

// 新增的消息定义
// 获取Crontab状态请求
message CrontabStatusRequest {
  // 可以为空，表示获取所有任务
  string stream_path = 1; // 可选，按流路径过滤
}

// 任务信息
message CrontabTaskInfo {
  uint32 plan_id = 1;           // 计划ID
  string plan_name = 2;         // 计划名称
  string stream_path = 3;       // 流路径
  bool is_recording = 4;        // 是否正在录制
  google.protobuf.Timestamp start_time = 5;  // 当前/下一个任务开始时间
  google.protobuf.Timestamp end_time = 6;    // 当前/下一个任务结束时间
  string time_range = 7;        // 时间范围（例如：09:00-10:00）
  string weekday = 8;           // 周几（例如：周一）
  string file_path = 9;         // 文件保存路径
  string fragment = 10;         // 分片设置
  uint32 elapsed_seconds = 11;  // 已运行时间（秒，仅对正在运行的任务有效）
  uint32 remaining_seconds = 12; // 剩余时间（秒）
  repeated TimeSlotInfo plan_slots = 13; // 完整的计划时间段列表
}

// 获取Crontab状态响应
message CrontabStatusResponse {
  int32 code = 1;                      // 响应码
  string message = 2;                  // 响应消息
  repeated CrontabTaskInfo running_tasks = 3;  // 当前正在执行的任务列表
  repeated CrontabTaskInfo next_tasks = 4;     // 下一个计划执行的任务列表
  uint32 total_running = 5;            // 正在运行的任务总数
  uint32 total_planned = 6;            // 计划中的任务总数
}