// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: sei.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InsertRequest struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	StreamPath       string                 `protobuf:"bytes,1,opt,name=streamPath,proto3" json:"streamPath,omitempty"`
	Data             string                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Type             uint32                 `protobuf:"varint,3,opt,name=type,proto3" json:"type,omitempty"`
	TargetStreamPath string                 `protobuf:"bytes,4,opt,name=targetStreamPath,proto3" json:"targetStreamPath,omitempty"`
	Format           string                 `protobuf:"bytes,5,opt,name=format,proto3" json:"format,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *InsertRequest) Reset() {
	*x = InsertRequest{}
	mi := &file_sei_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InsertRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InsertRequest) ProtoMessage() {}

func (x *InsertRequest) ProtoReflect() protoreflect.Message {
	mi := &file_sei_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InsertRequest.ProtoReflect.Descriptor instead.
func (*InsertRequest) Descriptor() ([]byte, []int) {
	return file_sei_proto_rawDescGZIP(), []int{0}
}

func (x *InsertRequest) GetStreamPath() string {
	if x != nil {
		return x.StreamPath
	}
	return ""
}

func (x *InsertRequest) GetData() string {
	if x != nil {
		return x.Data
	}
	return ""
}

func (x *InsertRequest) GetType() uint32 {
	if x != nil {
		return x.Type
	}
	return 0
}

func (x *InsertRequest) GetTargetStreamPath() string {
	if x != nil {
		return x.TargetStreamPath
	}
	return ""
}

func (x *InsertRequest) GetFormat() string {
	if x != nil {
		return x.Format
	}
	return ""
}

var File_sei_proto protoreflect.FileDescriptor

const file_sei_proto_rawDesc = "" +
	"\n" +
	"\tsei.proto\x12\x03sei\x1a\x1cgoogle/api/annotations.proto\x1a\fglobal.proto\"\x9b\x01\n" +
	"\rInsertRequest\x12\x1e\n" +
	"\n" +
	"streamPath\x18\x01 \x01(\tR\n" +
	"streamPath\x12\x12\n" +
	"\x04data\x18\x02 \x01(\tR\x04data\x12\x12\n" +
	"\x04type\x18\x03 \x01(\rR\x04type\x12*\n" +
	"\x10targetStreamPath\x18\x04 \x01(\tR\x10targetStreamPath\x12\x16\n" +
	"\x06format\x18\x05 \x01(\tR\x06format2k\n" +
	"\x03api\x12d\n" +
	"\x06insert\x12\x12.sei.InsertRequest\x1a\x17.global.SuccessResponse\"-\x82\xd3\xe4\x93\x02':\x04data\"\x1f/sei/api/insert/{streamPath=**}B\x1bZ\x19m7s.live/v5/plugin/sei/pbb\x06proto3"

var (
	file_sei_proto_rawDescOnce sync.Once
	file_sei_proto_rawDescData []byte
)

func file_sei_proto_rawDescGZIP() []byte {
	file_sei_proto_rawDescOnce.Do(func() {
		file_sei_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_sei_proto_rawDesc), len(file_sei_proto_rawDesc)))
	})
	return file_sei_proto_rawDescData
}

var file_sei_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_sei_proto_goTypes = []any{
	(*InsertRequest)(nil),      // 0: sei.InsertRequest
	(*pb.SuccessResponse)(nil), // 1: global.SuccessResponse
}
var file_sei_proto_depIdxs = []int32{
	0, // 0: sei.api.insert:input_type -> sei.InsertRequest
	1, // 1: sei.api.insert:output_type -> global.SuccessResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_sei_proto_init() }
func file_sei_proto_init() {
	if File_sei_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_sei_proto_rawDesc), len(file_sei_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sei_proto_goTypes,
		DependencyIndexes: file_sei_proto_depIdxs,
		MessageInfos:      file_sei_proto_msgTypes,
	}.Build()
	File_sei_proto = out.File
	file_sei_proto_goTypes = nil
	file_sei_proto_depIdxs = nil
}
