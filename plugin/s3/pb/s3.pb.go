// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.29.3
// source: s3.proto

package pb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	pb "m7s.live/v5/pb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UploadRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Filename      string                 `protobuf:"bytes,1,opt,name=filename,proto3" json:"filename,omitempty"`                          // File name
	Content       []byte                 `protobuf:"bytes,2,opt,name=content,proto3" json:"content,omitempty"`                            // File content
	ContentType   string                 `protobuf:"bytes,3,opt,name=content_type,json=contentType,proto3" json:"content_type,omitempty"` // MIME type
	Bucket        string                 `protobuf:"bytes,4,opt,name=bucket,proto3" json:"bucket,omitempty"`                              // Bucket name (optional, uses default if empty)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadRequest) Reset() {
	*x = UploadRequest{}
	mi := &file_s3_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadRequest) ProtoMessage() {}

func (x *UploadRequest) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadRequest.ProtoReflect.Descriptor instead.
func (*UploadRequest) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{0}
}

func (x *UploadRequest) GetFilename() string {
	if x != nil {
		return x.Filename
	}
	return ""
}

func (x *UploadRequest) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *UploadRequest) GetContentType() string {
	if x != nil {
		return x.ContentType
	}
	return ""
}

func (x *UploadRequest) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

type UploadResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *UploadData            `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadResponse) Reset() {
	*x = UploadResponse{}
	mi := &file_s3_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadResponse) ProtoMessage() {}

func (x *UploadResponse) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadResponse.ProtoReflect.Descriptor instead.
func (*UploadResponse) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{1}
}

func (x *UploadResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UploadResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *UploadResponse) GetData() *UploadData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UploadData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`    // S3 object key
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`    // Public URL
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"` // File size in bytes
	Etag          string                 `protobuf:"bytes,4,opt,name=etag,proto3" json:"etag,omitempty"`  // ETag from S3
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UploadData) Reset() {
	*x = UploadData{}
	mi := &file_s3_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UploadData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UploadData) ProtoMessage() {}

func (x *UploadData) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UploadData.ProtoReflect.Descriptor instead.
func (*UploadData) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{2}
}

func (x *UploadData) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *UploadData) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UploadData) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *UploadData) GetEtag() string {
	if x != nil {
		return x.Etag
	}
	return ""
}

type ListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Prefix        string                 `protobuf:"bytes,1,opt,name=prefix,proto3" json:"prefix,omitempty"`                   // Prefix filter
	MaxKeys       int32                  `protobuf:"varint,2,opt,name=max_keys,json=maxKeys,proto3" json:"max_keys,omitempty"` // Maximum number of keys to return
	Marker        string                 `protobuf:"bytes,3,opt,name=marker,proto3" json:"marker,omitempty"`                   // Pagination marker
	Bucket        string                 `protobuf:"bytes,4,opt,name=bucket,proto3" json:"bucket,omitempty"`                   // Bucket name (optional, uses default if empty)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListRequest) Reset() {
	*x = ListRequest{}
	mi := &file_s3_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListRequest) ProtoMessage() {}

func (x *ListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListRequest.ProtoReflect.Descriptor instead.
func (*ListRequest) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{3}
}

func (x *ListRequest) GetPrefix() string {
	if x != nil {
		return x.Prefix
	}
	return ""
}

func (x *ListRequest) GetMaxKeys() int32 {
	if x != nil {
		return x.MaxKeys
	}
	return 0
}

func (x *ListRequest) GetMarker() string {
	if x != nil {
		return x.Marker
	}
	return ""
}

func (x *ListRequest) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

type ListResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *ListData              `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListResponse) Reset() {
	*x = ListResponse{}
	mi := &file_s3_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListResponse) ProtoMessage() {}

func (x *ListResponse) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListResponse.ProtoReflect.Descriptor instead.
func (*ListResponse) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{4}
}

func (x *ListResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ListResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ListResponse) GetData() *ListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ListData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Objects       []*S3Object            `protobuf:"bytes,1,rep,name=objects,proto3" json:"objects,omitempty"`
	IsTruncated   bool                   `protobuf:"varint,2,opt,name=is_truncated,json=isTruncated,proto3" json:"is_truncated,omitempty"` // Whether there are more results
	NextMarker    string                 `protobuf:"bytes,3,opt,name=next_marker,json=nextMarker,proto3" json:"next_marker,omitempty"`     // Next pagination marker
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListData) Reset() {
	*x = ListData{}
	mi := &file_s3_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListData) ProtoMessage() {}

func (x *ListData) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListData.ProtoReflect.Descriptor instead.
func (*ListData) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{5}
}

func (x *ListData) GetObjects() []*S3Object {
	if x != nil {
		return x.Objects
	}
	return nil
}

func (x *ListData) GetIsTruncated() bool {
	if x != nil {
		return x.IsTruncated
	}
	return false
}

func (x *ListData) GetNextMarker() string {
	if x != nil {
		return x.NextMarker
	}
	return ""
}

type S3Object struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`                                       // Object key
	Size          int64                  `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`                                    // Object size in bytes
	LastModified  string                 `protobuf:"bytes,3,opt,name=last_modified,json=lastModified,proto3" json:"last_modified,omitempty"` // Last modified timestamp
	Etag          string                 `protobuf:"bytes,4,opt,name=etag,proto3" json:"etag,omitempty"`                                     // ETag
	StorageClass  string                 `protobuf:"bytes,5,opt,name=storage_class,json=storageClass,proto3" json:"storage_class,omitempty"` // Storage class
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *S3Object) Reset() {
	*x = S3Object{}
	mi := &file_s3_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *S3Object) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*S3Object) ProtoMessage() {}

func (x *S3Object) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use S3Object.ProtoReflect.Descriptor instead.
func (*S3Object) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{6}
}

func (x *S3Object) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *S3Object) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *S3Object) GetLastModified() string {
	if x != nil {
		return x.LastModified
	}
	return ""
}

func (x *S3Object) GetEtag() string {
	if x != nil {
		return x.Etag
	}
	return ""
}

func (x *S3Object) GetStorageClass() string {
	if x != nil {
		return x.StorageClass
	}
	return ""
}

type DeleteRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`       // Object key to delete
	Bucket        string                 `protobuf:"bytes,2,opt,name=bucket,proto3" json:"bucket,omitempty"` // Bucket name (optional, uses default if empty)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRequest) Reset() {
	*x = DeleteRequest{}
	mi := &file_s3_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRequest) ProtoMessage() {}

func (x *DeleteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRequest.ProtoReflect.Descriptor instead.
func (*DeleteRequest) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteRequest) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *DeleteRequest) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

type ConnectionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          uint32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	Data          *ConnectionData        `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionResponse) Reset() {
	*x = ConnectionResponse{}
	mi := &file_s3_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionResponse) ProtoMessage() {}

func (x *ConnectionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionResponse.ProtoReflect.Descriptor instead.
func (*ConnectionResponse) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{8}
}

func (x *ConnectionResponse) GetCode() uint32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ConnectionResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *ConnectionResponse) GetData() *ConnectionData {
	if x != nil {
		return x.Data
	}
	return nil
}

type ConnectionData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Connected     bool                   `protobuf:"varint,1,opt,name=connected,proto3" json:"connected,omitempty"`         // Whether connection is successful
	Endpoint      string                 `protobuf:"bytes,2,opt,name=endpoint,proto3" json:"endpoint,omitempty"`            // S3 endpoint
	Region        string                 `protobuf:"bytes,3,opt,name=region,proto3" json:"region,omitempty"`                // AWS region
	UseSsl        bool                   `protobuf:"varint,4,opt,name=use_ssl,json=useSsl,proto3" json:"use_ssl,omitempty"` // Whether SSL is enabled
	Bucket        string                 `protobuf:"bytes,5,opt,name=bucket,proto3" json:"bucket,omitempty"`                // Default bucket name
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConnectionData) Reset() {
	*x = ConnectionData{}
	mi := &file_s3_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConnectionData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConnectionData) ProtoMessage() {}

func (x *ConnectionData) ProtoReflect() protoreflect.Message {
	mi := &file_s3_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConnectionData.ProtoReflect.Descriptor instead.
func (*ConnectionData) Descriptor() ([]byte, []int) {
	return file_s3_proto_rawDescGZIP(), []int{9}
}

func (x *ConnectionData) GetConnected() bool {
	if x != nil {
		return x.Connected
	}
	return false
}

func (x *ConnectionData) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *ConnectionData) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ConnectionData) GetUseSsl() bool {
	if x != nil {
		return x.UseSsl
	}
	return false
}

func (x *ConnectionData) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

var File_s3_proto protoreflect.FileDescriptor

const file_s3_proto_rawDesc = "" +
	"\n" +
	"\bs3.proto\x12\x02s3\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\fglobal.proto\"\x80\x01\n" +
	"\rUploadRequest\x12\x1a\n" +
	"\bfilename\x18\x01 \x01(\tR\bfilename\x12\x18\n" +
	"\acontent\x18\x02 \x01(\fR\acontent\x12!\n" +
	"\fcontent_type\x18\x03 \x01(\tR\vcontentType\x12\x16\n" +
	"\x06bucket\x18\x04 \x01(\tR\x06bucket\"b\n" +
	"\x0eUploadResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12\"\n" +
	"\x04data\x18\x03 \x01(\v2\x0e.s3.UploadDataR\x04data\"X\n" +
	"\n" +
	"UploadData\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\x12\x12\n" +
	"\x04size\x18\x03 \x01(\x03R\x04size\x12\x12\n" +
	"\x04etag\x18\x04 \x01(\tR\x04etag\"p\n" +
	"\vListRequest\x12\x16\n" +
	"\x06prefix\x18\x01 \x01(\tR\x06prefix\x12\x19\n" +
	"\bmax_keys\x18\x02 \x01(\x05R\amaxKeys\x12\x16\n" +
	"\x06marker\x18\x03 \x01(\tR\x06marker\x12\x16\n" +
	"\x06bucket\x18\x04 \x01(\tR\x06bucket\"^\n" +
	"\fListResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12 \n" +
	"\x04data\x18\x03 \x01(\v2\f.s3.ListDataR\x04data\"v\n" +
	"\bListData\x12&\n" +
	"\aobjects\x18\x01 \x03(\v2\f.s3.S3ObjectR\aobjects\x12!\n" +
	"\fis_truncated\x18\x02 \x01(\bR\visTruncated\x12\x1f\n" +
	"\vnext_marker\x18\x03 \x01(\tR\n" +
	"nextMarker\"\x8e\x01\n" +
	"\bS3Object\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x12\n" +
	"\x04size\x18\x02 \x01(\x03R\x04size\x12#\n" +
	"\rlast_modified\x18\x03 \x01(\tR\flastModified\x12\x12\n" +
	"\x04etag\x18\x04 \x01(\tR\x04etag\x12#\n" +
	"\rstorage_class\x18\x05 \x01(\tR\fstorageClass\"9\n" +
	"\rDeleteRequest\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x16\n" +
	"\x06bucket\x18\x02 \x01(\tR\x06bucket\"j\n" +
	"\x12ConnectionResponse\x12\x12\n" +
	"\x04code\x18\x01 \x01(\rR\x04code\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\x12&\n" +
	"\x04data\x18\x03 \x01(\v2\x12.s3.ConnectionDataR\x04data\"\x93\x01\n" +
	"\x0eConnectionData\x12\x1c\n" +
	"\tconnected\x18\x01 \x01(\bR\tconnected\x12\x1a\n" +
	"\bendpoint\x18\x02 \x01(\tR\bendpoint\x12\x16\n" +
	"\x06region\x18\x03 \x01(\tR\x06region\x12\x17\n" +
	"\ause_ssl\x18\x04 \x01(\bR\x06useSsl\x12\x16\n" +
	"\x06bucket\x18\x05 \x01(\tR\x06bucket2\xc8\x02\n" +
	"\x03api\x12J\n" +
	"\x06Upload\x12\x11.s3.UploadRequest\x1a\x12.s3.UploadResponse\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\"\x0e/s3/api/upload\x12?\n" +
	"\x04List\x12\x0f.s3.ListRequest\x1a\x10.s3.ListResponse\"\x14\x82\xd3\xe4\x93\x02\x0e\x12\f/s3/api/list\x12U\n" +
	"\x06Delete\x12\x11.s3.DeleteRequest\x1a\x17.global.SuccessResponse\"\x1f\x82\xd3\xe4\x93\x02\x19*\x17/s3/api/delete/{key=**}\x12]\n" +
	"\x0fCheckConnection\x12\x16.google.protobuf.Empty\x1a\x16.s3.ConnectionResponse\"\x1a\x82\xd3\xe4\x93\x02\x14\x12\x12/s3/api/connectionB\x1aZ\x18m7s.live/v5/plugin/s3/pbb\x06proto3"

var (
	file_s3_proto_rawDescOnce sync.Once
	file_s3_proto_rawDescData []byte
)

func file_s3_proto_rawDescGZIP() []byte {
	file_s3_proto_rawDescOnce.Do(func() {
		file_s3_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_s3_proto_rawDesc), len(file_s3_proto_rawDesc)))
	})
	return file_s3_proto_rawDescData
}

var file_s3_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_s3_proto_goTypes = []any{
	(*UploadRequest)(nil),      // 0: s3.UploadRequest
	(*UploadResponse)(nil),     // 1: s3.UploadResponse
	(*UploadData)(nil),         // 2: s3.UploadData
	(*ListRequest)(nil),        // 3: s3.ListRequest
	(*ListResponse)(nil),       // 4: s3.ListResponse
	(*ListData)(nil),           // 5: s3.ListData
	(*S3Object)(nil),           // 6: s3.S3Object
	(*DeleteRequest)(nil),      // 7: s3.DeleteRequest
	(*ConnectionResponse)(nil), // 8: s3.ConnectionResponse
	(*ConnectionData)(nil),     // 9: s3.ConnectionData
	(*emptypb.Empty)(nil),      // 10: google.protobuf.Empty
	(*pb.SuccessResponse)(nil), // 11: global.SuccessResponse
}
var file_s3_proto_depIdxs = []int32{
	2,  // 0: s3.UploadResponse.data:type_name -> s3.UploadData
	5,  // 1: s3.ListResponse.data:type_name -> s3.ListData
	6,  // 2: s3.ListData.objects:type_name -> s3.S3Object
	9,  // 3: s3.ConnectionResponse.data:type_name -> s3.ConnectionData
	0,  // 4: s3.api.Upload:input_type -> s3.UploadRequest
	3,  // 5: s3.api.List:input_type -> s3.ListRequest
	7,  // 6: s3.api.Delete:input_type -> s3.DeleteRequest
	10, // 7: s3.api.CheckConnection:input_type -> google.protobuf.Empty
	1,  // 8: s3.api.Upload:output_type -> s3.UploadResponse
	4,  // 9: s3.api.List:output_type -> s3.ListResponse
	11, // 10: s3.api.Delete:output_type -> global.SuccessResponse
	8,  // 11: s3.api.CheckConnection:output_type -> s3.ConnectionResponse
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_s3_proto_init() }
func file_s3_proto_init() {
	if File_s3_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_s3_proto_rawDesc), len(file_s3_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_s3_proto_goTypes,
		DependencyIndexes: file_s3_proto_depIdxs,
		MessageInfos:      file_s3_proto_msgTypes,
	}.Build()
	File_s3_proto = out.File
	file_s3_proto_goTypes = nil
	file_s3_proto_depIdxs = nil
}
