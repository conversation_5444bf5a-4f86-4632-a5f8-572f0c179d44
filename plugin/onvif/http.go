package plugin_onvif

import (
	"encoding/json"
	"net/http"
)

func Success(w http.ResponseWriter, data interface{}) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(http.StatusOK)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"code": 0,
		"data": data,
	})
}

func BadRequest(w http.ResponseWriter, err error) {
	w.<PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>eader(http.StatusBadRequest)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"code": 400,
		"msg":  err.<PERSON>rror(),
	})
}

func InternalError(w http.ResponseWriter, err error) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.Write<PERSON>ead<PERSON>(http.StatusInternalServerError)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"code": 500,
		"msg":  err.<PERSON><PERSON><PERSON>(),
	})
}

func NotFound(w http.ResponseWriter) {
	w.<PERSON><PERSON>().Set("Content-Type", "application/json")
	w.<PERSON><PERSON><PERSON><PERSON>(http.StatusNotFound)
	json.NewEncoder(w).Encode(map[string]interface{}{
		"code": 404,
		"msg":  "not found",
	})
}

func UnmarshalRequest(r *http.Request, v interface{}) error {
	return json.NewDecoder(r.Body).Decode(v)
}
