syntax = "proto3";
package pb;
option go_package = "m7s.live/v5/pb";

import "google/api/annotations.proto";

message LoginRequest {
  string username = 1;
  string password = 2;
}

message LoginSuccess {
  string token = 1;
  UserInfo userInfo = 2;
}

message LoginResponse {
  int32 code = 1;
  string message = 2;
  LoginSuccess data = 3;
}

message LogoutRequest {
  string token = 1;
}

message LogoutResponse {
  int32 code = 1;
  string message = 2;
}

message UserInfoRequest {
  string token = 1;
}

message UserInfo {
  string username = 1;
  int64 expires_at = 2;  // Token expiration timestamp
}

message UserInfoResponse {
  int32 code = 1;
  string message = 2;
  UserInfo data = 3;
}

service Auth {
  rpc Login(LoginRequest) returns (LoginResponse) {
    option (google.api.http) = {
      post: "/api/auth/login"
      body: "*"
    };
  }
  rpc Logout(LogoutRequest) returns (LogoutResponse) {
    option (google.api.http) = {
      post: "/api/auth/logout"
      body: "*"
    };
  }
  rpc GetUserInfo(UserInfoRequest) returns (UserInfoResponse) {
    option (google.api.http) = {
      get: "/api/auth/userinfo"
    };
  }
} 