syntax = "proto3";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/any.proto";
package global;
option go_package="m7s.live/v5/pb";

service api {
  rpc SysInfo (google.protobuf.Empty) returns (SysInfoResponse) {
    option (google.api.http) = {
      get: "/api/sysinfo"
    };
  }
  rpc DisabledPlugins (google.protobuf.Empty) returns (DisabledPluginsResponse) {
    option (google.api.http) = {
      get: "/api/plugins/disabled"
    };
  }
  rpc Summary (google.protobuf.Empty) returns (SummaryResponse) {
    option (google.api.http) = {
      get: "/api/summary"
    };
  }
  rpc Shutdown (RequestWithId) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/shutdown"
    };
  }
  rpc Restart (RequestWithId) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/restart"
    };
  }
  rpc TaskTree (google.protobuf.Empty) returns (TaskTreeResponse) {
    option (google.api.http) = {
      get: "/api/task/tree"
    };
  }
  rpc StopTask (RequestWithId64) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/task/stop/{id}"
    };
  }
  rpc RestartTask (RequestWithId64) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/task/restart/{id}"
    };
  }
  rpc StreamList (StreamListRequest) returns (StreamListResponse) {
    option (google.api.http) = {
      get: "/api/stream/list"
    };
  }
  rpc WaitList (google.protobuf.Empty) returns (StreamWaitListResponse) {
    option (google.api.http) = {
      get: "/api/stream/waitlist"
    };
  }
  rpc StreamInfo (StreamSnapRequest) returns (StreamInfoResponse) {
    option (google.api.http) = {
      get: "/api/stream/info/{streamPath=**}"
    };
  }
  rpc PauseStream(StreamSnapRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/pause/{streamPath=**}"
      body: "*"
    };
  }
  rpc ResumeStream(StreamSnapRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/resume/{streamPath=**}"
      body: "*"
    };
  }
  rpc SetStreamSpeed(SetStreamSpeedRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/speed/{streamPath=**}"
      body: "*"
    };
  }
  rpc SeekStream(SeekStreamRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/seek/{streamPath=**}"
      body: "*"
    };
  }
  rpc GetSubscribers(SubscribersRequest) returns (SubscribersResponse) {
    option (google.api.http) = {
      get: "/api/subscribers/{streamPath=**}"
    };
  }
  rpc AudioTrackSnap (StreamSnapRequest) returns (TrackSnapShotResponse) {
    option (google.api.http) = {
      get: "/api/audiotrack/snap/{streamPath=**}"
    };
  }
  rpc VideoTrackSnap (StreamSnapRequest) returns (TrackSnapShotResponse) {
    option (google.api.http) = {
      get: "/api/videotrack/snap/{streamPath=**}"
    };
  }
  rpc ChangeSubscribe (ChangeSubscribeRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/subscribe/change/{id}/{streamPath=**}"
      body: "*"
    };
  }
  rpc GetStreamAlias (google.protobuf.Empty) returns (StreamAliasListResponse) {
    option (google.api.http) = {
      get: "/api/stream/alias/list"
    };
  }
  rpc SetStreamAlias (SetStreamAliasRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/alias"
      body: "*"
    };
  }
  rpc StopPublish(StreamSnapRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/stream/stop/{streamPath=**}"
      body: "*"
    };
  }
  rpc StopSubscribe (RequestWithId) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/subscribe/stop/{id}"
      body: "*"
    };
  }
  rpc GetConfigFile (google.protobuf.Empty) returns (GetConfigFileResponse) {
    option (google.api.http) = {
      get: "/api/config/file"
    };
  }
  rpc UpdateConfigFile (UpdateConfigFileRequest) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/config/file/update"
      body: "content"
    };
  }
  rpc GetConfig (GetConfigRequest) returns (GetConfigResponse) {
    option (google.api.http) = {
      get: "/api/config/get/{name}"
    };
  }
  rpc GetFormily (GetConfigRequest) returns (GetConfigResponse) {
    option (google.api.http) = {
      get: "/api/config/formily/{name}"
    };
  }

  rpc GetPullProxyList (google.protobuf.Empty) returns (PullProxyListResponse) {
    option (google.api.http) = {
      get: "/api/proxy/pull/list"
      additional_bindings {
        get: "/api/device/list"
      }
    };
  }
  rpc AddPullProxy (PullProxyInfo) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/pull/add"
      body: "*"
      additional_bindings {
        post: "/api/device/add"
        body: "*"
      }
    };
  }
  rpc RemovePullProxy (RequestWithId) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/pull/remove/{id}"
      body: "*"
      additional_bindings {
        post: "/api/device/remove/{id}"
        body: "*"
      }
    };
  }
  rpc UpdatePullProxy (PullProxyInfo) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/pull/update"
      body: "*"
      additional_bindings {
        post: "/api/device/update"
        body: "*"
      }
    };
  }
  rpc GetPushProxyList (google.protobuf.Empty) returns (PushProxyListResponse) {
    option (google.api.http) = {
      get: "/api/proxy/push/list"
    };
  }
  rpc AddPushProxy (PushProxyInfo) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/push/add"
      body: "*"
    };
  }
  rpc RemovePushProxy (RequestWithId) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/push/remove/{id}"
      body: "*"
    };
  }
  rpc UpdatePushProxy (PushProxyInfo) returns (SuccessResponse) {
    option (google.api.http) = {
      post: "/api/proxy/push/update"
      body: "*"
    };
  }
  rpc GetRecording (google.protobuf.Empty) returns (RecordingListResponse) {
    option (google.api.http) = {
      get: "/api/record/list"
    };
  }
  rpc GetTransformList (google.protobuf.Empty) returns (TransformListResponse) {
    option (google.api.http) = {
      get: "/api/transform/list"
    };
  }
  rpc GetRecordList (ReqRecordList) returns (RecordResponseList) {
    option (google.api.http) = {
      get: "/api/record/{type}/list/{streamPath=**}"
    };
  }
  rpc GetEventRecordList (ReqRecordList) returns (EventRecordResponseList) {
    option (google.api.http) = {
      get: "/api/record/{type}/event/list/{streamPath=**}"
    };
  }
  rpc GetRecordCatalog (ReqRecordCatalog) returns (ResponseCatalog) {
    option (google.api.http) = {
      get: "/api/record/{type}/catalog"
    };
  }
  rpc DeleteRecord (ReqRecordDelete) returns (ResponseDelete) {
    option (google.api.http) = {
      post: "/api/record/{type}/delete/{streamPath=**}"
      body: "*"
    };
  }
}

message DisabledPluginsResponse {
  int32 code = 1;
  string message = 2;
  repeated PluginInfo data = 3;
}

message GetConfigRequest {
  string name = 1;
}

message Formily {
  string type = 1;
  map<string, Formily> properties = 2;
  string component = 3;
  map<string, google.protobuf.Any> componentProps = 4;
}

message FormilyResponse {
  string type = 1;
  map<string, Formily> properties = 2;
}

message ConfigData {
  string file = 1;
  string modified = 2;
  string merged = 3;
}

message GetConfigFileResponse {
  uint32 code = 1;
  string message = 2;
  string data = 3;
}

message GetConfigResponse {
  uint32 code = 1;
  string message = 2;
  ConfigData data = 3;
}

message UpdateConfigFileRequest {
  string content = 1;
}

message ModifyConfigRequest {
  string name = 1;
  string yaml = 2;
}

message NetWorkInfo {
	string name = 1;
	uint64 receive = 2;
	uint64 sent = 3;
	uint64 receiveSpeed = 4;
	uint64 sentSpeed = 5;
}

message Usage {
  uint64 total = 1;
  uint64 free = 2;
  uint64 used = 3;
  float usage = 4;
}

message SummaryResponse {
  string address = 1;
  Usage memory = 2;
  float cpuUsage = 3;
  Usage hardDisk = 4;
  repeated NetWorkInfo netWork = 5;
  int32 streamCount = 6;
  int32 subscribeCount = 7;
  int32 pullCount = 8;
  int32 pushCount = 9;
  int32 recordCount = 10;
  int32 transformCount = 11;
}

message PluginInfo {
  string name = 1;
  repeated string pushAddr = 2;
  repeated string playAddr = 3;
  map<string, string> description = 4;
}

message SysInfoData  {
  google.protobuf.Timestamp startTime = 1;
  string localIP = 2;
  string publicIP = 3;
  string version = 4;
  string goVersion = 5;
  string os = 6;
  string arch = 7;
  int32 cpus = 8;
  repeated PluginInfo plugins = 9;
}

message SysInfoResponse {
  int32 code = 1;
  string message = 2;
  SysInfoData data = 3;
}

message TaskTreeData {
  uint32 id = 1;
  uint32 type = 2;
  string owner = 3;
  google.protobuf.Timestamp startTime = 4;
  map<string, string> description = 5;
  repeated TaskTreeData children = 6;
  uint32 state = 7;
  TaskTreeData blocked = 8;
  uint64 pointer = 9;
  string startReason = 10;
}

message TaskTreeResponse {
  int32 code = 1;
  string message = 2;
  TaskTreeData data = 3;
}

message StreamListRequest {
  int32 pageNum = 1;
  int32 pageSize = 2;
}

message StreamListResponse {
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  int32 pageNum = 4;
  int32 pageSize = 5;
  repeated StreamInfo data = 6;
}

message StreamWaitListResponse {
  map<string, int32> list = 1;
}

message StreamSnapRequest {
  string streamPath = 1;
}

message StreamInfoResponse {
  int32 code = 1;
  string message = 2;
  StreamInfo data = 3;
}

message StreamInfo {
  string path = 1;
  int32 state = 2;
  int32 subscribers = 3;
  AudioTrackInfo audioTrack = 4;
  VideoTrackInfo videoTrack = 5;
  google.protobuf.Timestamp startTime = 6;
  string pluginName = 7;
  string type = 8;
  string meta = 9;
  bool isPaused = 10;
  int32 gop = 11;
  float speed = 12;
  google.protobuf.Duration bufferTime = 13;
  bool stopOnIdle = 14;
  repeated RecordingDetail recording = 15;
}

message RecordingDetail {
  string filePath = 1;
  string mode = 2;
  google.protobuf.Duration fragment = 3;
  bool append = 4;
  string pluginName = 5;
  uint64 pointer = 6;
}

message Wrap {
  uint32 timestamp = 1;
  uint32 size = 2;
  string data = 3;
}

message TrackSnapShot {
  uint32 sequence = 1;
  uint32 timestamp = 2;
  google.protobuf.Timestamp writeTime = 3;
  bool keyFrame = 4;
  repeated Wrap wrap = 5;
}

message MemoryBlock {
  uint32 s = 1;
  uint32 e = 2;
}

message MemoryBlockGroup {
  uint32 size = 1;
  repeated MemoryBlock list = 2;
}

message AudioTrackInfo {
  string codec = 1;
  string delta = 2;
  string meta = 3;
  uint32 bps = 4;
  uint32 bps_out = 5;
  uint32 fps = 6;
  uint32 sampleRate = 7;
  uint32 channels =8;
}

message TrackSnapShotData {
  repeated TrackSnapShot ring = 1;
  uint32 ringDataSize = 2;
  map<uint32, uint32> reader = 3;
  repeated MemoryBlockGroup memory = 4;
}

message TrackSnapShotResponse {
  int32 code = 1;
  string message = 2;
  TrackSnapShotData data = 3;
}

message VideoTrackInfo {
  string codec = 1;
  string delta = 2;
  string meta = 3;
  uint32 bps = 4;
  uint32 bps_out = 5;
  uint32 fps = 6;
  uint32 width = 7;
  uint32 height =8;
  uint32 gop = 9;
}

message SuccessResponse {
  int32 code = 1;
  string message = 2;
}

message RequestWithId {
  uint32 id = 1;
  string streamPath = 2;
}

message RequestWithId64 {
  uint64 id = 1;
}

message ChangeSubscribeRequest {
  uint32 id = 1;
  string streamPath = 2;
}

message SubscribersRequest {
  string streamPath = 1;
  int32 pageNum = 2;
  int32 pageSize = 3;
}

message RingReaderSnapShot {
  uint32 sequence = 1;
  uint32 timestamp = 2;
  uint32 delay = 3;
  int32 state = 4;
  uint32 bps = 5;
}

message SubscriberSnapShot {
  uint32 id = 1;
  google.protobuf.Timestamp startTime = 2;
  RingReaderSnapShot audioReader = 3;
  RingReaderSnapShot videoReader = 4;
  string meta = 5;
  google.protobuf.Duration bufferTime = 6;
  int32 subMode = 7;
  int32 syncMode = 8;
  string pluginName = 9;
  string type = 10;
  string remoteAddr = 11;
}

message SubscribersResponse {
  int32 code = 1;
  string message = 2;
  int32 total = 3;
  int32 pageNum = 4;
  int32 pageSize = 5;
  repeated SubscriberSnapShot data = 6;
}

message PullProxyListResponse {
  int32 code = 1;
  string message = 2;
  repeated PullProxyInfo data = 3;
}

message PullProxyInfo {
  uint32 ID = 1;
  google.protobuf.Timestamp createTime = 2;
  google.protobuf.Timestamp updateTime = 3; // 更新时间
  uint32 parentID = 4; // 父设备ID
  string name = 5; // 设备名称
  string type = 6; // 设备类型
  uint32 status = 7; // 设备状态
  string pullURL = 8; // 拉流地址
  bool pullOnStart = 9; // 启动时拉流
  
  bool stopOnIdle = 10; // 空闲时停止拉流
  bool audio = 11; // 是否拉取音频
  string description = 12; // 设备描述
  string recordPath = 13; // 录制路径
  google.protobuf.Duration recordFragment = 14; // 录制片段长度
  uint32 rtt = 15; // 平均RTT
  string streamPath = 16; // 流路径
}

message PushProxyInfo {
  uint32 ID = 1;
  google.protobuf.Timestamp createTime = 2;
  google.protobuf.Timestamp updateTime = 3;
  uint32 parentID = 4; // 父设备ID
  string name = 5; // 设备名称
  string type = 6; // 设备类型
  uint32 status = 7; // 设备状态
  string pushURL = 8; // 推流地址
  bool pushOnStart = 9; // 启动时推流
  bool audio = 10; // 是否推音频
  string description = 11; // 设备描述
  uint32 rtt = 12; // 平均RTT
  string streamPath = 13; // 流路径
}

message PushProxyListResponse {
  int32 code = 1;
  string message = 2;
  repeated PushProxyInfo data = 3;
}


message SetStreamAliasRequest {
  string streamPath = 1;
  string alias = 2;
  bool autoRemove = 3;
}

message StreamAlias {
  string streamPath = 1;
  string alias = 2;
  bool autoRemove = 3;
  uint32 status = 4;
}

message StreamAliasListResponse {
  int32 code = 1;
  string message = 2;
  repeated StreamAlias data = 3;
}

message SetStreamSpeedRequest {
  string streamPath = 1;
  float speed = 2;
}

message SeekStreamRequest {
  string streamPath = 1;
  uint32 timeStamp = 2;
}

message Recording {
  string streamPath = 1;
  google.protobuf.Timestamp startTime = 2;
  string type = 3;
  uint64 pointer = 4;
}

message RecordingListResponse {
  int32 code = 1;
  string message = 2;
  repeated Recording data = 3;
}

message PushInfo {
  string streamPath = 1;
  string targetURL = 2;
  google.protobuf.Timestamp startTime = 3;
  string status = 4;
}

message PushListResponse {
  int32 code = 1;
  string message = 2;
  repeated PushInfo data = 3;
}

message AddPushRequest {
  string streamPath = 1;
  string targetURL = 2;
}

message Transform {
  string streamPath = 1;
  string target = 2;
  string pluginName = 3;
  string config = 4;
}

message TransformListResponse {
  int32 code = 1;
  string message = 2;
  repeated Transform data = 3;
}

message ReqRecordList {
  string streamPath = 1;
  string range = 2;
  string start = 3;
  string end = 4;
  uint32 pageNum = 5;
  uint32 pageSize = 6;
  string type = 7;
  string eventLevel = 8;
}

message RecordFile {
  uint32 id = 1;
  string filePath = 2;
  string streamPath = 3;
  google.protobuf.Timestamp startTime = 4;
  google.protobuf.Timestamp endTime = 5;
}

message EventRecordFile {
  uint32 id = 1;
  string filePath = 2;
  string streamPath = 3;
  google.protobuf.Timestamp startTime = 4;
  google.protobuf.Timestamp endTime = 5;
  string eventId = 6;
  string eventLevel = 7;
  string eventName = 8;
  string eventDesc = 9;
}

message RecordResponseList {
  int32 code = 1;
  string message = 2;
  uint32 total = 3;
  uint32 pageNum = 4;
  uint32 pageSize = 5;
  repeated RecordFile data = 6;
}

message EventRecordResponseList {
  int32 code = 1;
  string message = 2;
  uint32 total = 3;
  uint32 pageNum = 4;
  uint32 pageSize = 5;
  repeated EventRecordFile data = 6;
}

message Catalog {
  string streamPath = 1;
  uint32 count = 2;
  google.protobuf.Timestamp startTime = 3;
  google.protobuf.Timestamp endTime = 4;
}

message ResponseCatalog {
  int32 code = 1;
  string message = 2;
  repeated Catalog data = 3;
}

message ReqRecordDelete {
  string streamPath = 1;
  repeated uint32 ids = 2;
  string startTime = 3;
  string endTime = 4;
  string range = 5;
  string type = 6;
}

message ResponseDelete {
  int32 code = 1;
  string message = 2;
  repeated RecordFile data = 3;
}

message ReqRecordCatalog {
  string type = 1;
}