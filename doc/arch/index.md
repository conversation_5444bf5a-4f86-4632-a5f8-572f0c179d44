# Architecture Design

## Directory Structure

[catalog.md](./catalog.md)

## Audio/Video Streaming System

### Relay Mechanism

[relay.md](./relay.md)

### Alias Mechanism

[alias.md](./alias.md)

### Authentication Mechanism

[auth.md](./auth.md)

## Plugin System

### Lifecycle

[plugin.md](./plugin.md)

### Plugin Development

[plugin/README.md](../../plugin/README.md)

## Task System

[task.md](./task.md)

## Configuration Mechanism

[config.md](./config.md)

## Logging System

[log.md](./log.md)

## Database Mechanism

[db.md](./db.md)

## GRPC Service

[grpc.md](./grpc.md)

## HTTP Service

[http.md](./http.md)

## Admin Service

[admin.md](./admin.md) 