package pkg

import (
	"bytes"
	"fmt"

	"github.com/deepch/vdk/codec/h264parser"
	"github.com/deepch/vdk/codec/h265parser"
	"m7s.live/v5/pkg/codec"
	"m7s.live/v5/pkg/util"
)

var _ IAVFrame = (*RawAudio)(nil)

type RawAudio struct {
	Sample
}

func (r *RawAudio) Parse(data any) (err error) {
	*r = *data.(*RawAudio)
	return
}

func (r *RawAudio) Mux(from *Sample) (err error) {
	r.InitRecycleIndexes(0)
	r.Memory = from.Raw.(util.Memory)
	r.ICodecCtx = from.GetBase()
	return
}

func (r *RawAudio) String() string {
	return fmt.Sprintf("RawAudio{FourCC: %s, Timestamp: %s, Size: %d}", r.FourCC, r.Timestamp, r.<PERSON><PERSON>)
}

var _ IAVFrame = (*H26xFrame)(nil)

type H26xFrame struct {
	Sample
}

func (h *H26xFrame) Parse(data any) (err error) {
	var hasVideoFrame bool
	pub := data.(*H26xFrame)
	old := h.ICodecCtx
	*h = *pub
	h.ICodecCtx = old
	var vps, sps, pps []byte
	for _, nalu := range h.Raw.(Nalus) {
		if pub.ICodecCtx.FourCC() == codec.FourCC_H265 {
			switch codec.ParseH265NALUType(nalu.Buffers[0][0]) {
			case h265parser.NAL_UNIT_VPS:
				vps = nalu.ToBytes()
			case h265parser.NAL_UNIT_SPS:
				sps = nalu.ToBytes()
			case h265parser.NAL_UNIT_PPS:
				pps = nalu.ToBytes()
			case h265parser.NAL_UNIT_CODED_SLICE_BLA_W_LP,
				h265parser.NAL_UNIT_CODED_SLICE_BLA_W_RADL,
				h265parser.NAL_UNIT_CODED_SLICE_BLA_N_LP,
				h265parser.NAL_UNIT_CODED_SLICE_IDR_W_RADL,
				h265parser.NAL_UNIT_CODED_SLICE_IDR_N_LP,
				h265parser.NAL_UNIT_CODED_SLICE_CRA:
				h.IDR = true
			case 1, 2, 3, 4, 5, 6, 7, 8, 9:
				hasVideoFrame = true
			}
		} else {
			switch codec.ParseH264NALUType(nalu.Buffers[0][0]) {
			case codec.NALU_SPS:
				sps = nalu.ToBytes()
			case codec.NALU_PPS:
				pps = nalu.ToBytes()
			case codec.NALU_IDR_Picture:
				h.IDR = true
			case codec.NALU_Non_IDR_Picture:
				hasVideoFrame = true
			}
		}
	}
	if pub.ICodecCtx.FourCC() == codec.FourCC_H265 {
		if vps != nil && sps != nil && pps != nil {
			var codecData h265parser.CodecData
			codecData, err = h265parser.NewCodecDataFromVPSAndSPSAndPPS(vps, sps, pps)
			if old == nil || !bytes.Equal(codecData.Record, old.(*codec.H265Ctx).Record) {
				h.ICodecCtx = &codec.H265Ctx{
					CodecData: codecData,
				}
			}
		}
	} else {
		if sps != nil && pps != nil {
			var codecData h264parser.CodecData
			codecData, err = h264parser.NewCodecDataFromSPSAndPPS(sps, pps)
			if old == nil || !bytes.Equal(codecData.Record, old.(*codec.H264Ctx).Record) {
				h.ICodecCtx = &codec.H264Ctx{
					CodecData: codecData,
				}
			}
		}
	}

	// Return ErrSkip if no video frames are present (only metadata NALUs)
	if !hasVideoFrame && !h.IDR {
		return ErrSkip
	}
	return
}

func (h *H26xFrame) Demux() error {
	return nil
}

func (h *H26xFrame) Mux(frame *Sample) error {
	return nil
}

func (h *H26xFrame) String() string {
	return fmt.Sprintf("H26xFrame{FourCC: %s, Timestamp: %s, CTS: %s}", h.FourCC, h.Timestamp, h.CTS)
}
