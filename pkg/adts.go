package pkg

import (
	"bytes"
	"fmt"
	"time"

	"github.com/deepch/vdk/codec/aacparser"
	"m7s.live/v5/pkg/codec"
	"m7s.live/v5/pkg/util"
)

var _ IAVFrame = (*ADTS)(nil)

type ADTS struct {
	DTS time.Duration
	Sample
}

func (A *ADTS) Parse(data any) (err error) {
	old := A.ICodecCtx
	*A = *data.(*ADTS)
	A.ICodecCtx = old
	A.Timestamp = A.DTS * time.Millisecond / 90
	if old == nil {
		var ctx = &codec.AACCtx{}
		var reader = A.<PERSON>eader()
		var adts []byte
		adts, err = reader.ReadBytes(7)
		if err != nil {
			return
		}
		var hdrlen, framelen, samples int
		ctx.Config, hdrlen, framelen, samples, err = aacparser.ParseADTSHeader(adts)
		if err != nil {
			return
		}
		b := &bytes.Buffer{}
		aacparser.WriteMPEG4AudioConfig(b, ctx.Config)
		ctx.ConfigBytes = b.Bytes()
		A.ICodecCtx = ctx
		if false {
			println("ADTS", "hdrlen", hdrlen, "framelen", framelen, "samples", samples, "config", ctx.Config)
		}
		// track.Info("ADTS", "hdrlen", hdrlen, "framelen", framelen, "samples", samples)
	} else {

	}
	return
}

func (A *ADTS) Demux() (err error) {
	var reader = A.NewReader()
	err = reader.Skip(7)
	var mem util.Memory
	reader.Range(mem.AppendOne)
	A.Raw = mem
	return
}

func (A *ADTS) Mux(frame *Sample) (err error) {
	A.InitRecycleIndexes(1)
	A.DTS = frame.Timestamp * 90 / time.Millisecond
	aacCtx, ok := frame.GetBase().(*codec.AACCtx)
	if !ok {
		A.Append(frame.Raw.(util.Memory).Buffers...)
		return
	}
	adts := A.NextN(7)
	raw := frame.Raw.(util.Memory)
	aacparser.FillADTSHeader(adts, aacCtx.Config, raw.Size/aacCtx.GetSampleSize(), raw.Size)
	A.Append(raw.Buffers...)
	return
}

func (A *ADTS) String() string {
	return fmt.Sprintf("ADTS{size:%d}", A.Size)
}
