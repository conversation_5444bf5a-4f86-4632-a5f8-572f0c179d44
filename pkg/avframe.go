package pkg

import (
	"net"
	"sync"
	"time"

	"github.com/bluenviron/mediacommon/pkg/codecs/av1"
	"m7s.live/v5/pkg/codec"
	"m7s.live/v5/pkg/util"
)

type (
	IAudioCodecCtx interface {
		codec.ICodecCtx
		GetSampleRate() int
		GetChannels() int
		GetSampleSize() int
	}
	IVideoCodecCtx interface {
		codec.ICodecCtx
		Width() int
		Height() int
	}
	IDataFrame interface {
	}
	// Source -> Parse -> Demux -> (ConvertCtx) -> Mux(GetAllocator) -> Recycle
	IAVFrame interface {
		Parse(*Sample) (err error) //set data to ringbuffer get codec info, idr
		Demux() error              // demux to raw format
		Mux(*Sample) error         // mux from origin format
		String() string
	}
	ISequenceCodecCtx[T any] interface {
		GetSequenceFrame() T
	}
	BaseSample struct {
		Raw       any // 裸格式用于转换的中间格式
		IDR       bool
		Timestamp time.Duration // 绝对时间戳
		CTS       time.Duration // composition time stamp
	}
	Sample struct {
		codec.ICodecCtx
		util.RecyclableMemory
		demux      func() error
		parse, mux func(*Sample) error
		*BaseSample
	}
	Nalus []util.Memory

	AudioData = util.Memory

	OBUs AudioData

	AVFrame struct {
		DataFrame
		*Sample
		Wraps []*Sample // 封装格式
	}

	AVRing    = util.Ring[AVFrame]
	DataFrame struct {
		sync.RWMutex
		discard   bool
		Sequence  uint32    // 在一个Track中的序号
		WriteTime time.Time // 写入时间,可用于比较两个帧的先后
	}
)

func (frame *Sample) Parse(data *Sample) error {
	frame.RecyclableMemory = data.RecyclableMemory
	*frame.BaseSample = *data.BaseSample
	if frame.parse != nil {
		return frame.parse(frame)
	}
	return nil
}

func (frame *Sample) Mux(from *Sample) error {
	if frame.mux == nil {
		frame.ICodecCtx = from.GetBase()
		return nil
	}
	return frame.mux(from)
}

func (frame *Sample) Demux() error {
	if frame.demux == nil {
		frame.Raw = frame.Memory
		return nil
	}
	return frame.demux()
}

func (frame *Sample) Convert(from, to IAVFrame) (err error) {
	fromSampe, toSample := from.(*Sample), to.(*Sample)
	if fromSampe.Raw == nil {
		if err = from.Demux(); err != nil {
			return
		}
	}
	toSample.SetAllocator(fromSampe.GetAllocator())
	toSample.BaseSample = fromSampe.BaseSample
	return to.Mux(fromSampe)
}

// 90Hz
func (b *BaseSample) GetDTS() time.Duration {
	return b.Timestamp * 90 / time.Millisecond
}

func (b *BaseSample) GetPTS() time.Duration {
	return (b.Timestamp + b.CTS) * 90 / time.Millisecond
}

func (b *BaseSample) SetDTS(dts time.Duration) {
	b.Timestamp = dts * time.Millisecond / 90
}

func (b *BaseSample) SetPTS(pts time.Duration) {
	b.CTS = pts*time.Millisecond/90 - b.Timestamp
}

func (frame *AVFrame) Reset() {
	if len(frame.Wraps) > 0 {
		for _, wrap := range frame.Wraps {
			wrap.Recycle()
		}
	}
}

func (frame *AVFrame) Discard() {
	frame.discard = true
	frame.Reset()
}

func (df *DataFrame) StartWrite() (success bool) {
	if df.discard {
		return
	}
	if df.TryLock() {
		return true
	}
	df.discard = true
	return
}

func (df *DataFrame) Ready() {
	df.WriteTime = time.Now()
	df.Unlock()
}

func (nalus *Nalus) H264Type() codec.H264NALUType {
	return codec.ParseH264NALUType((*nalus)[0].Buffers[0][0])
}

func (nalus *Nalus) H265Type() codec.H265NALUType {
	return codec.ParseH265NALUType((*nalus)[0].Buffers[0][0])
}

func (nalus *Nalus) Append(bytes []byte) {
	*nalus = append(*nalus, util.Memory{Buffers: net.Buffers{bytes}, Size: len(bytes)})
}

func (nalus *Nalus) ParseAVCC(reader *util.MemoryReader, naluSizeLen int) error {
	for reader.Length > 0 {
		l, err := reader.ReadBE(naluSizeLen)
		if err != nil {
			return err
		}
		var mem util.Memory
		reader.RangeN(int(l), mem.AppendOne)
		*nalus = append(*nalus, mem)
	}
	return nil
}

func (obus *OBUs) ParseAVCC(reader *util.MemoryReader) error {
	var obuHeader av1.OBUHeader
	startLen := reader.Length
	for reader.Length > 0 {
		offset := reader.Size - reader.Length
		b, err := reader.ReadByte()
		if err != nil {
			return err
		}
		err = obuHeader.Unmarshal([]byte{b})
		if err != nil {
			return err
		}
		// if log.Trace {
		// 	vt.Trace("obu", zap.Any("type", obuHeader.Type), zap.Bool("iframe", vt.Value.IFrame))
		// }
		obuSize, _, _ := reader.LEB128Unmarshal()
		end := reader.Size - reader.Length
		size := end - offset + int(obuSize)
		reader = &util.MemoryReader{Memory: reader.Memory, Length: startLen - offset}
		obu, err := reader.ReadBytes(size)
		if err != nil {
			return err
		}
		(*AudioData)(obus).AppendOne(obu)
	}
	return nil
}
