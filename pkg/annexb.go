package pkg

import (
	"bytes"
	"fmt"
	"io"

	"github.com/deepch/vdk/codec/h264parser"
	"github.com/deepch/vdk/codec/h265parser"

	"m7s.live/v5/pkg/codec"
	"m7s.live/v5/pkg/util"
)

type AnnexB Sample

// Parse implements pkg.IAVFrame.
func (a *AnnexB) Parse(data *Sample) (err error) {
	(*Sample)(a).Parse(data)
	err = a.Demux()
	if err != nil {
		return
	}
	var vps, sps, pps []byte
	for _, nalu := range a.Raw.(Nalus) {
		if data.FourCC() == codec.FourCC_H265 {
			switch codec.ParseH265NALUType(nalu.Buffers[0][0]) {
			case h265parser.NAL_UNIT_VPS:
				vps = nalu.ToBytes()
			case h265parser.NAL_UNIT_SPS:
				sps = nalu.ToBytes()
			case h265parser.NAL_UNIT_PPS:
				pps = nalu.ToBytes()
			case h265parser.NAL_UNIT_CODED_SLICE_BLA_W_LP,
				h265parser.NAL_UNIT_CODED_SLICE_BLA_W_RADL,
				h265parser.NAL_UNIT_CODED_SLICE_BLA_N_LP,
				h265parser.NAL_UNIT_CODED_SLICE_IDR_W_RADL,
				h265parser.NAL_UNIT_CODED_SLICE_IDR_N_LP,
				h265parser.NAL_UNIT_CODED_SLICE_CRA:
				a.IDR = true
			}
		} else {
			switch codec.ParseH264NALUType(nalu.Buffers[0][0]) {
			case codec.NALU_SPS:
				sps = nalu.ToBytes()
			case codec.NALU_PPS:
				pps = nalu.ToBytes()
			case codec.NALU_IDR_Picture:
				a.IDR = true
			}
		}
	}
	if data.FourCC() == codec.FourCC_H265 {
		if vps != nil && sps != nil && pps != nil {
			var codecData h265parser.CodecData
			codecData, err = h265parser.NewCodecDataFromVPSAndSPSAndPPS(vps, sps, pps)
			if a.ICodecCtx == nil || !bytes.Equal(codecData.Record, a.ICodecCtx.(*codec.H265Ctx).Record) {
				a.ICodecCtx = &codec.H265Ctx{
					CodecData: codecData,
				}
			}
		}
	} else {
		if sps != nil && pps != nil {
			var codecData h264parser.CodecData
			codecData, err = h264parser.NewCodecDataFromSPSAndPPS(sps, pps)
			if a.ICodecCtx == nil || !bytes.Equal(codecData.Record, a.ICodecCtx.(*codec.H264Ctx).Record) {
				a.ICodecCtx = &codec.H264Ctx{
					CodecData: codecData,
				}
			}
		}
	}
	if a.ICodecCtx == nil {
		return fmt.Errorf("no codec ctx")
	}
	return
}

// String implements pkg.IAVFrame.
func (a *AnnexB) String() string {
	return fmt.Sprintf("%d %d", a.Timestamp, a.Memory.Size)
}

// Demux implements pkg.IAVFrame.
func (a *AnnexB) Demux() (err error) {
	var nalus Nalus
	var lastFourBytes [4]byte
	var b byte
	var shallow util.Memory
	shallow.Append(a.Buffers...)
	reader := shallow.NewReader()

	gotNalu := func() {
		var nalu util.Memory
		for buf := range reader.ClipFront {
			nalu.AppendOne(buf)
		}
		nalus = append(nalus, nalu)

	}

	for {
		b, err = reader.ReadByte()
		if err == nil {
			copy(lastFourBytes[:], lastFourBytes[1:])
			lastFourBytes[3] = b
			var startCode = 0
			if lastFourBytes == codec.NALU_Delimiter2 {
				startCode = 4
			} else if [3]byte(lastFourBytes[1:]) == codec.NALU_Delimiter1 {
				startCode = 3
			}
			if startCode > 0 && reader.Offset() >= 3 {
				if reader.Offset() == 3 {
					startCode = 3
				}
				reader.Unread(startCode)
				if reader.Offset() > 0 {
					gotNalu()
				}
				reader.Skip(startCode)
				for range reader.ClipFront {
				}
			}
		} else if err == io.EOF {
			if reader.Offset() > 0 {
				gotNalu()
			}
			err = nil
			break
		}
	}
	a.Raw = nalus
	return
}

func (a *AnnexB) Mux(fromBase *Sample) (err error) {
	a.InitRecycleIndexes(0)
	delimiter2 := codec.NALU_Delimiter2[:]
	a.AppendOne(delimiter2)
	if fromBase.IDR {
		switch ctx := fromBase.GetBase().(type) {
		case *codec.H264Ctx:
			a.Append(ctx.SPS(), delimiter2, ctx.PPS(), delimiter2)
		case *codec.H265Ctx:
			a.Append(ctx.SPS(), delimiter2, ctx.PPS(), delimiter2, ctx.VPS(), delimiter2)
		}
	}
	for i, nalu := range fromBase.Raw.(Nalus) {
		if i > 0 {
			a.AppendOne(codec.NALU_Delimiter1[:])
		}
		a.Append(nalu.Buffers...)
	}
	return
}
