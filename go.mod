module m7s.live/v5

go 1.23.0

require (
	github.com/IOTechSystems/onvif v1.2.0
	github.com/VictoriaMetrics/VictoriaMetrics v1.102.0
	github.com/asavie/xdp v0.3.3
	github.com/aws/aws-sdk-go v1.55.7
	github.com/beevik/etree v1.4.1
	github.com/bluenviron/gohlslib v1.4.0
	github.com/c0deltin/duckdb-driver v0.1.0
	github.com/cilium/ebpf v0.15.0
	github.com/cloudwego/goref v0.0.0-20240724113447-685d2a9523c8
	github.com/deepch/vdk v0.0.27
	github.com/disintegration/imaging v1.6.2
	github.com/emiago/sipgo v0.29.0
	github.com/go-delve/delve v1.23.0
	github.com/gobwas/ws v1.3.2
	github.com/golang-jwt/jwt/v5 v5.2.1
	github.com/golang/freetype v0.0.0-20170609003504-e2365dfdc4a0
	github.com/google/gopacket v1.1.19
	github.com/google/uuid v1.6.0
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.19.1
	github.com/husanpao/ip v0.0.0-20220711082147-73160bb611a8
	github.com/icholy/digest v0.1.22
	github.com/jinzhu/copier v0.4.0
	github.com/mark3labs/mcp-go v0.27.0
	github.com/mattn/go-sqlite3 v1.14.24
	github.com/mcuadros/go-defaults v1.2.0
	github.com/mozillazg/go-pinyin v0.20.0
	github.com/ncruces/go-sqlite3 v0.18.1
	github.com/ncruces/go-sqlite3/gormlite v0.18.0
	github.com/pion/interceptor v0.1.37
	github.com/pion/logging v0.2.2
	github.com/pion/rtcp v1.2.15
	github.com/pion/rtp v1.8.10
	github.com/pion/sdp/v3 v3.0.9
	github.com/pion/webrtc/v4 v4.0.7
	github.com/quic-go/qpack v0.5.1
	github.com/quic-go/quic-go v0.50.1
	github.com/rs/zerolog v1.33.0
	github.com/samber/slog-common v0.17.1
	github.com/shirou/gopsutil/v4 v4.24.8
	github.com/stretchr/testify v1.10.0
	github.com/valyala/fasthttp v1.61.0
	github.com/vishvananda/netlink v1.1.0
	github.com/yapingcat/gomedia v0.0.0-20240601043430-920523f8e5c7
	golang.org/x/image v0.22.0
	golang.org/x/text v0.24.0
	google.golang.org/genproto/googleapis/api v0.0.0-20240711142825-46eb208f015d
	google.golang.org/grpc v1.65.0
	google.golang.org/protobuf v1.34.2
	gorm.io/driver/mysql v1.5.7
	gorm.io/driver/postgres v1.5.9
	gorm.io/gorm v1.30.0
)

require (
	github.com/VictoriaMetrics/easyproto v0.1.4 // indirect
	github.com/VictoriaMetrics/fastcache v1.12.2 // indirect
	github.com/VictoriaMetrics/metrics v1.35.1 // indirect
	github.com/VictoriaMetrics/metricsql v0.76.0 // indirect
	github.com/abema/go-mp4 v1.2.0 // indirect
	github.com/andybalholm/brotli v1.1.1 // indirect
	github.com/asticode/go-astikit v0.30.0 // indirect
	github.com/asticode/go-astits v1.13.0 // indirect
	github.com/benburkert/openpgp v0.0.0-20160410205803-c2471f86866c // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/chromedp/cdproto v0.0.0-20240202021202-6d0b6a386732 // indirect
	github.com/chromedp/sysutil v1.0.0 // indirect
	github.com/clbanning/mxj/v2 v2.7.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/elgs/gostrgen v0.0.0-20220325073726-0c3e00d082f6 // indirect
	github.com/go-ole/go-ole v1.2.6 // indirect
	github.com/go-sql-driver/mysql v1.7.0 // indirect
	github.com/gobwas/httphead v0.1.0 // indirect
	github.com/gobwas/pool v0.2.1 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/hashicorp/golang-lru v1.0.2 // indirect
	github.com/jackc/pgpassfile v1.0.0 // indirect
	github.com/jackc/pgservicefile v0.0.0-20221227161230-091c0ba34f0a // indirect
	github.com/jackc/pgx/v5 v5.5.5 // indirect
	github.com/jackc/puddle/v2 v2.2.1 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/jmespath/go-jmespath v0.4.0 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/klauspost/compress v1.18.0 // indirect
	github.com/lufia/plan9stats v0.0.0-20211012122336-39d0f177ccd0 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/marcboeker/go-duckdb v1.0.5 // indirect
	github.com/mattn/go-colorable v0.1.13 // indirect
	github.com/mattn/go-isatty v0.0.20 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/ncruces/julianday v1.0.0 // indirect
	github.com/pion/datachannel v1.5.10 // indirect
	github.com/pion/dtls/v3 v3.0.4 // indirect
	github.com/pion/ice/v4 v4.0.3 // indirect
	github.com/pion/mdns/v2 v2.0.7 // indirect
	github.com/pion/randutil v0.1.0 // indirect
	github.com/pion/sctp v1.8.35 // indirect
	github.com/pion/srtp/v3 v3.0.4 // indirect
	github.com/pion/stun/v3 v3.0.0 // indirect
	github.com/pion/transport/v3 v3.0.7 // indirect
	github.com/pion/turn/v4 v4.0.0 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/power-devops/perfstat v0.0.0-20210106213030-5aafc221ea8c // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.55.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/samber/lo v1.44.0 // indirect
	github.com/satori/go.uuid v1.2.1-0.20181028125025-b2ce2384e17b // indirect
	github.com/shoenig/go-m1cpu v0.1.6 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spf13/cast v1.7.1 // indirect
	github.com/tetratelabs/wazero v1.8.0 // indirect
	github.com/tklauser/go-sysconf v0.3.12 // indirect
	github.com/tklauser/numcpus v0.6.1 // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fastjson v1.6.4 // indirect
	github.com/valyala/fastrand v1.1.0 // indirect
	github.com/valyala/gozstd v1.21.1 // indirect
	github.com/valyala/histogram v1.2.0 // indirect
	github.com/valyala/quicktemplate v1.8.0 // indirect
	github.com/vishvananda/netns v0.0.0-20191106174202-0a2b9b5464df // indirect
	github.com/wlynxg/anet v0.0.5 // indirect
	github.com/yosida95/uritemplate/v3 v3.0.2 // indirect
	github.com/yusufpapurcu/wmi v1.2.4 // indirect
	golang.org/x/arch v0.8.0 // indirect
	golang.org/x/sync v0.13.0 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20240711142825-46eb208f015d // indirect
)

require (
	github.com/alchemy/rotoslog v0.2.2
	github.com/bluenviron/mediacommon v1.11.1-0.20240525122142-20163863aa75
	github.com/chromedp/chromedp v0.9.5
	github.com/datarhei/gosrt v0.7.0
	github.com/go-task/slim-sprig v0.0.0-20230315185526-52ccab3ef572 // indirect
	github.com/google/pprof v0.0.0-20240409012703-83162a5b38cd // indirect
	github.com/gorilla/websocket v1.5.1
	github.com/ianlancetaylor/demangle v0.0.0-20240912202439-0a2b6291aafd
	github.com/onsi/ginkgo/v2 v2.9.5 // indirect
	github.com/phsym/console-slog v0.3.1
	github.com/prometheus/client_golang v1.20.4
	github.com/quangngotan95/go-m3u8 v0.1.0
	go.uber.org/mock v0.5.0 // indirect
	golang.org/x/crypto v0.37.0
	golang.org/x/exp v0.0.0-20240716175740-e3f259677ff7
	golang.org/x/mod v0.19.0 // indirect
	golang.org/x/net v0.39.0
	golang.org/x/sys v0.32.0
	golang.org/x/tools v0.23.0 // indirect
	gopkg.in/yaml.v3 v3.0.1
)
