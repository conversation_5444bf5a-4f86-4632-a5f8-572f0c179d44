global:
  loglevel: debug
  http: :9040
#  tcp: :50052
#
rtsp:
 pull:
   live/test: rtsp://127.0.0.1:8554/live/test

#transcode:
#  onpub:
#    transform:
#      ^live.+:
#        output:
#          - target: rtmp://localhost/trans/$0/small
#            conf: -loglevel debug -c:a aac -c:v h264_videotoolbox -vf scale=320:240

mp4:
  onsub:
    pull:
      ^vod(\d)/(.+)$: live/$2
  onpub:
    record:
      ^live/.+:
        fragment: 30s
        filepath: record/mp4

