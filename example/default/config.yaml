global:
  location:
    "^/hdl/(.*)": "/flv/$1"
  log_level: debug
  admin:
    enable_login: false
  http:
    listen_addr: :8080
    listen_addr_tls: :8443
    # static_dir: ./static
    # static_prefix: /static
  # publish:
  #   pubaudio: false
monitor:
  enable: false
srt:
  listen_addr: :6000
  passphrase: foobarfoobar
gb28181:
  enable: false
  auto_invite: false
  media_ip: ************ #流媒体收流IP
  sip_ip: ************ #SIP通讯IP
  sip:
    listen_addr:
      - udp::5060
  # pull:
  #   live/test: dump/34020000001320000001
  onsub:
    pull:
      ^\d{20}/\d{20}$: $0
      ^gb_\d+/(.+)$: $1
  #      .* : $0
  platforms:
    - enable: false #是否启用平台
      name: "测试平台" #平台名称
      servergbid: "34020000002000000002" #上级平台GBID
      servergbdomain: "3402000000" #上级平台GB域
      serverip: ***********06 #上级平台IP
      serverport: 5061 #上级平台端口
      devicegbid: "34020000002000000001" #本平台设备GBID
      deviceip: ***********06 #本平台设备IP
      deviceport: 5060 #本平台设备端口
      username: "34020000002000000001" #SIP账号
      password: "123456" #SIP密码
      expires: 3600 #注册有效期，单位秒
      keeptimeout: 60 #注册保持超时时间，单位秒
      civilCode: "340200" #行政区划代码
      manufacturer: "Monibuca" #设备制造商
      model: "GB28181" #设备型号
      address: "江苏南京" #设备地址
      register_way: 1
      platformchannels:
        - platformservergbid: "34020000002000000002" #上级平台GBID
          channeldbid: "34020000001110000003_34020000001320000005" #通道DBID,格式为设备ID_通道ID
mp4:
  # enable: false
  # publish:
  #   delayclosetimeout: 3s
  # onpub:
  #   record:
  #     ^live/.+:
  #       fragment: 10s
  #       filepath: record/$0

  onsub:
    pull:
      ^vod_mp4_\d+/(.+)$: $1
cascadeserver:
  quic:
    listenaddr: :44944
# llhls:
#   onpub:
#     transform:
#       .* : 1s x 7
flv:
  #   onpub:
  #     record:
  #       ^live/.+:
  #         fragment: 1m
  #         filepath: record/$0
  publish:
    delayclosetimeout: 3s
  onsub:
    pull:
      ^vod_flv_\d+/(.+)$: $1
#   pull:
#     live/test: https://livecb.alicdn.com/mediaplatform/afb241b3-408c-42dd-b665-04d22b64f9df.flv?auth_key=1734575216-0-0-c62721303ce751c8e5b2c95a2ec242a0&F=pc&source=34675810_null_live_detail&ali_flv_retain=2
hls:
  # onsub:
  #   pull:
  #     ^vod_hls_\d+/(.+)$: $1
  # pull:
  #   live/test: https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_4x3/gear3/prog_index.m3u8
#   onpub:
#     transform:
#       .* : 5s x 3
#rtsp:
#  pull:
# live/test: rtsp://admin:<EMAIL>:1554/Streaming/Channels/101
#    live/test: rtsp://admin:1qaz2wsx3EDC@localhost:8554/live/test
rtmp:
  tcp:
    write_buffer: 1048576 # 1MB
  # pull:
  #   live/test: rtmp://*************:21222/live/1581F6Q8X24BU00G01HK
webrtc:
  port: udp:9000
  enabledc: false
snap:
  enable: false
  onpub:
    transform:
      .+:
        output:
          - watermark:
              text: "abcd" # 水印文字内容
              fontpath: /Users/<USER>/Library/Fonts/MapleMono-NF-CN-Medium.ttf # 水印字体文件路径
              fontcolor: "rgba(255,165,0,1)" # 水印字体颜色，支持rgba格式
              fontsize: 36 # 水印字体大小
              offsetx: 0 # 水印位置X偏移
              offsety: 0 # 水印位置Y偏移
            timeinterval: 1s # 截图时间间隔
            savepath: "snaps" # 截图保存路径
            iframeinterval: 3 # 间隔多少帧截图
            querytimedelta: 3 # 查询截图时允许的最大时间差（秒）

crypto:
  enable: false
  isstatic: false
  algo: aes_ctr # 加密算法 支持 aes_ctr xor_c
  encryptlen: 1024
  secret:
    key: your key
    iv: your iv
  onpub:
    transform:
      .*: $0
onvif:
  enable: false
  discoverinterval: 3 # 发现设备的间隔，单位秒，默认30秒，建议比rtsp插件的重连间隔大点
  autopull: true
  autoadd: true
  interfaces: # 设备发现指定网卡，以及该网卡对应IP段的全局默认账号密码，支持多网卡
    - interfacename: 以太网 # 网卡名称 或者"以太网" "eth0"等，使用ipconfig 或者 ifconfig 查看网卡名称
      username: admin # onvif 账号
      password: admin # onvif 密码
    # - interfacename: WLAN 2 # 网卡2
    #   username: admin
    #   password: admin
  # devices: # 可以给指定设备配置单独的密码
  #   - ip: ***********
  #     username: admin
  #     password: '123'
  #   - ip: ***********
  #     username: admin
  #     password: '456'
