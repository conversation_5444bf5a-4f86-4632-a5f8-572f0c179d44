# Admin 服务机制

Monibuca 提供了强大的管理服务支持，用于系统监控、配置管理、插件管理等管理功能。本文档详细说明了 Admin 服务的实现机制和使用方法。

## 服务架构

### 1. UI 界面

Admin 服务通过加载 `admin.zip` 文件来提供 Web 管理界面。该界面具有以下特点：

- 统一的管理界面入口
- 可调用所有服务器提供的 HTTP 接口
- 响应式设计，支持多种设备访问
- 模块化的功能组织

### 2. 配置管理

Admin 服务的配置位于全局配置（global）中的 admin 节，包括：

```yaml
admin:
  enableLogin: false    # 是否启用登录机制
  filePath: admin.zip  # 管理界面文件路径
  homePage: home      # 管理界面首页
  users:             # 用户列表（仅在启用登录机制时生效）
    - username: admin   # 用户名
      password: admin   # 密码
      role: admin      # 角色，可选值：admin、user
```

当 `enableLogin` 为 false 时，所有用户都以匿名用户身份访问。
当启用登录机制且数据库中没有用户时，系统会自动创建一个默认管理员账户（用户名：admin，密码：admin）。

### 3. 认证机制

Admin 提供专门的用户登录验证接口，用于：

- 用户身份验证
- 访问令牌管理（JWT）
- 权限控制
- 会话管理

### 4. 接口规范

所有的 Admin API 都需要遵循以下规范：

- 响应格式统一包含 code、message、data 字段
- 成功响应使用 code = 0
- 错误处理采用统一的错误响应格式
- 必须进行权限验证

## 功能模块

### 1. 系统监控

- CPU 使用率监控
- 内存使用情况
- 网络带宽统计
- 磁盘使用情况
- 系统运行时间
- 在线用户统计

### 2. 插件管理

- 插件启用/禁用
- 插件配置修改
- 插件状态查看
- 插件版本管理
- 插件依赖检查

### 3. 流媒体管理

- 在线流列表查看
- 流状态监控
- 流控制（开始/停止）
- 流信息统计
- 录制管理
- 转码任务管理

## 安全机制

### 1. 认证机制

- JWT 令牌认证
- 会话超时控制
- IP 白名单控制

### 2. 权限控制

- 基于角色的访问控制（RBAC）
- 细粒度的权限管理
- 操作审计日志
- 敏感操作确认

## 最佳实践

1. 安全性
   - 使用 HTTPS 加密
   - 实施强密码策略
   - 定期更新密钥
   - 监控异常访问

2. 性能优化
   - 合理的缓存策略
   - 分页查询优化
   - 异步处理耗时操作

3. 可维护性
   - 完整的操作日志
   - 清晰的错误提示
   - 配置热更新 