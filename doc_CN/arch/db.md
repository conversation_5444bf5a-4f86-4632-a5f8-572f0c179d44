# 数据库机制

Monibuca 提供了数据库支持功能，可以在全局配置和插件中分别配置和使用数据库。

## 配置说明

### 全局配置

在全局配置中可以通过以下字段配置数据库：

```yaml
global:
  dsn: "数据库连接字符串"
  dbType: "数据库类型"
```

### 插件配置

每个插件也可以单独配置数据库：

```yaml
pluginName:
  dsn: "数据库连接字符串"
  dbType: "数据库类型"
```

## 数据库初始化流程

### 全局数据库初始化

1. 服务器启动时，如果配置了 `dsn`，会尝试连接数据库
2. 连接成功后会自动迁移以下模型:
   - User 用户表
   - PullProxy 拉流代理表
   - PushProxy 推流代理表
   - StreamAliasDB 流别名表

3. 如果开启了登录功能(`Admin.EnableLogin = true`)，会根据配置文件创建或更新用户
4. 如果数据库中没有任何用户，会创建一个默认的管理员账户:
   - 用户名: admin
   - 密码: admin
   - 角色: admin

### 插件数据库初始化

1. 插件初始化时会检查插件配置中的 `dsn`
2. 如果插件配置的 `dsn` 与全局配置相同，则直接使用全局数据库连接
3. 如果插件配置了不同的 `dsn`，则会创建新的数据库连接
4. 如果插件实现了 Recorder 接口，会自动迁移 RecordStream 表

## 数据库使用

### 全局数据库访问

可以通过 Server 实例访问全局数据库:

```go
server.DB
```

### 插件数据库访问

插件可以通过自身实例访问数据库:

```go
plugin.DB
```

## 注意事项

1. 数据库连接失败会导致相应的功能被禁用
2. 插件使用独立数据库时需要自行管理数据库连接
3. 数据库迁移失败会导致插件被禁用
4. 建议在可能的情况下复用全局数据库连接，避免创建过多连接

## 内置数据表

### User 表
用于存储用户信息，包含以下字段:
- Username: 用户名
- Password: 密码
- Role: 角色(admin/user)

### PullProxy 表
用于存储拉流代理配置

### PushProxy 表
用于存储推流代理配置

### StreamAliasDB 表
用于存储流别名配置

### RecordStream 表
用于存储录制相关信息(仅在插件实现 Recorder 接口时创建)
